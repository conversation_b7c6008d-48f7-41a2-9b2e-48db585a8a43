// SPDX-License-Identifier: MIT
// Copyright contributors to the kepler.gl project

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Button, SidePanelSection, PanelLabel, Input, DropdownSelect, DatasetTabs, LoadingSpinner } from '@kepler.gl/components';
import { addDataToMap } from '@kepler.gl/actions';

// Configuração da API
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:5000';

// Estilos para o painel de Turfeiras
const StyledTurfeirasPanel = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  padding: 16px;
`;

const StyledPanelHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const StyledPanelContent = styled.div`
  flex-grow: 1;
  overflow-y: auto;
  padding-right: 6px;
`;

const StyledAnalysisForm = styled.div`
  margin-top: 12px;
  padding: 12px;
  background-color: ${props => props.theme.panelBackground};
  border-radius: 4px;
`;

const StyledInputGroup = styled.div`
  margin-bottom: 12px;
`;

const StyledButtonGroup = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  gap: 8px;
`;

const StyledInfoBox = styled.div`
  margin: 12px 0;
  padding: 8px 12px;
  background-color: ${props => props.theme.notificationColors.info};
  color: white;
  border-radius: 4px;
  font-size: 12px;
`;

/**
 * Serviço para comunicação com a API de Turfeiras
 */
class TurfeirasApiService {
  /**
   * Carrega dados de turfeiras do backend e adiciona ao mapa Kepler.gl
   */
  static async loadTurfeirasData(params: any, dispatch: any): Promise<any> {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/turfeiras/data`, { params });
      
      if (response.data && response.data.datasets) {
        // Utiliza a ação addDataToMap do Kepler.gl para adicionar os dados diretamente ao mapa
        dispatch(addDataToMap({
          datasets: response.data.datasets,
          options: {
            centerMap: true,
          }
        }));
        
        return response.data;
      }
      
      throw new Error('Dados inválidos recebidos da API');
    } catch (error) {
      console.error('Erro ao carregar dados de turfeiras:', error);
      throw error;
    }
  }
  
  /**
   * Executa uma análise específica e retorna os resultados para visualização
   */
  static async runAnalysis(analysisType: string, params: any, dispatch: any): Promise<any> {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/turfeiras/analysis/${analysisType}`, params);
      
      if (response.data && response.data.results) {
        // Adiciona os resultados da análise como uma nova camada ou dataset
        dispatch(addDataToMap({
          datasets: [{
            info: {
              id: `analysis-${analysisType}-${Date.now()}`,
              label: `Resultado da Análise: ${analysisType}`,
              color: [255, 140, 0]
            },
            data: response.data.results
          }],
          options: {
            centerMap: false
          }
        }));
        
        return response.data;
      }
      
      throw new Error('Resultados de análise inválidos recebidos da API');
    } catch (error) {
      console.error(`Erro ao executar análise ${analysisType}:`, error);
      throw error;
    }
  }
  
  /**
   * Obtém a lista de análises disponíveis
   */
  static async getAvailableAnalyses(): Promise<any[]> {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/turfeiras/analyses`);
      return response.data.analyses || [];
    } catch (error) {
      console.error('Erro ao obter análises disponíveis:', error);
      return [];
    }
  }
  
  /**
   * Obtém as configurações das camadas recomendadas para visualização de turfeiras
   */
  static async getRecommendedLayerConfigs(): Promise<any[]> {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/turfeiras/layer-configs`);
      return response.data.layerConfigs || [];
    } catch (error) {
      console.error('Erro ao obter configurações de camadas:', error);
      return [];
    }
  }
}

/**
 * Componente principal para o painel de Turfeiras integrado ao Kepler.gl
 */
function TurfeirasPanel(props: any) {
  // Estado local para armazenar dados e estado do UI
  const [analysisType, setAnalysisType] = useState('');
  const [analysisParams, setAnalysisParams] = useState<Record<string, any>>({});
  const [availableAnalyses, setAvailableAnalyses] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [message, setMessage] = useState<{text: string; type: string} | null>(null);

  const { dispatch } = props;

  // Carrega a lista de análises disponíveis ao montar o componente
  useEffect(() => {
    async function loadAnalyses() {
      setIsLoading(true);
      try {
        const analyses = await TurfeirasApiService.getAvailableAnalyses();
        setAvailableAnalyses(analyses);
      } catch (error) {
        showMessage('Erro ao carregar análises disponíveis', 'error');
      } finally {
        setIsLoading(false);
      }
    }
    
    loadAnalyses();
  }, []);

  // Função para atualizar os parâmetros da análise
  const handleParamChange = (key: string, value: any) => {
    setAnalysisParams(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Função para mostrar mensagens de feedback
  const showMessage = (text: string, type = 'info', duration = 3000) => {
    setMessage({ text, type });
    if (duration) {
      setTimeout(() => setMessage(null), duration);
    }
  };

  // Função para carregar dados de turfeiras
  const handleLoadData = async () => {
    setIsLoading(true);
    try {
      await TurfeirasApiService.loadTurfeirasData({}, dispatch);
      showMessage('Dados de turfeiras carregados com sucesso!');
    } catch (error) {
      showMessage('Erro ao carregar dados de turfeiras', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Função para executar uma análise
  const handleRunAnalysis = async () => {
    if (!analysisType) {
      showMessage('Selecione um tipo de análise', 'warning');
      return;
    }
    
    setIsLoading(true);
    try {
      await TurfeirasApiService.runAnalysis(analysisType, analysisParams, dispatch);
      showMessage(`Análise "${analysisType}" executada com sucesso!`);
    } catch (error) {
      showMessage(`Erro ao executar análise ${analysisType}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Função para aplicar configurações de camada recomendadas
  const handleApplyRecommendedLayers = async () => {
    setIsLoading(true);
    try {
      const layerConfigs = await TurfeirasApiService.getRecommendedLayerConfigs();
      // Implementar a lógica para aplicar as configurações...
      showMessage('Configurações de camadas aplicadas com sucesso!');
    } catch (error) {
      showMessage('Erro ao aplicar configurações de camadas', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Renderização do painel
  return (
    <StyledTurfeirasPanel>
      <StyledPanelHeader>
        <h3>Análise de Turfeiras</h3>
      </StyledPanelHeader>
      
      {isLoading && <LoadingSpinner />}
      
      {message && (
        <StyledInfoBox className={`notification-${message.type}`}>
          {message.text}
        </StyledInfoBox>
      )}
      
      <Tabs
        activeTab={activeTab}
        onChange={setActiveTab}
        tabs={[
          { name: 'Dados', active: activeTab === 0 },
          { name: 'Análises', active: activeTab === 1 },
          { name: 'Configurações', active: activeTab === 2 }
        ]}
      />
      
      <StyledPanelContent>
        {activeTab === 0 && (
          <SidePanelSection>
            <PanelLabel>Dados de Turfeiras</PanelLabel>
            <p>Carregue os dados de turfeiras para visualização e análise.</p>
            <StyledButtonGroup>
              <Button onClick={handleLoadData}>
                Carregar Dados
              </Button>
            </StyledButtonGroup>
          </SidePanelSection>
        )}
        
        {activeTab === 1 && (
          <SidePanelSection>
            <PanelLabel>Análises Disponíveis</PanelLabel>
            <StyledAnalysisForm>
              <StyledInputGroup>
                <label>Tipo de Análise</label>
                <Select
                  options={availableAnalyses.map(a => ({
                    label: a.name,
                    value: a.id
                  }))}
                  value={analysisType}
                  onChange={setAnalysisType}
                  placeholder="Selecione uma análise..."
                />
              </StyledInputGroup>
              
              {analysisType && (
                <>
                  {/* Aqui renderizamos os campos de parâmetros dinamicamente com base no tipo de análise selecionado */}
                  {availableAnalyses
                    .find(a => a.id === analysisType)?.parameters
                    ?.map(param => (
                      <StyledInputGroup key={param.id}>
                        <label>{param.name}</label>
                        <Input
                          type={param.type}
                          value={analysisParams[param.id] || ''}
                          onChange={e => handleParamChange(param.id, e.target.value)}
                          placeholder={param.placeholder}
                        />
                      </StyledInputGroup>
                    ))}
                  
                  <StyledButtonGroup>
                    <Button onClick={handleRunAnalysis} width="100%">
                      Executar Análise
                    </Button>
                  </StyledButtonGroup>
                </>
              )}
            </StyledAnalysisForm>
          </SidePanelSection>
        )}
        
        {activeTab === 2 && (
          <SidePanelSection>
            <PanelLabel>Configurações de Visualização</PanelLabel>
            <p>Aplique configurações recomendadas para visualização de turfeiras.</p>
            <StyledButtonGroup>
              <Button onClick={handleApplyRecommendedLayers}>
                Aplicar Configurações Recomendadas
              </Button>
            </StyledButtonGroup>
          </SidePanelSection>
        )}
      </StyledPanelContent>
    </StyledTurfeirasPanel>
  );
}

export default TurfeirasPanel;
