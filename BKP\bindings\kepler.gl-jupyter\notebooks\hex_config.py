# SPDX-License-Identifier: MIT
# Copyright contributors to the kepler.gl project

config = {u'version': u'v1', u'config': {u'visState': {u'layers': [{u'type': u'hexagonId', u'visualChannels': {u'sizeField': {u'type': u'integer', u'name': u'value'}, u'coverageField': None, u'colorScale': u'quantize', u'coverageScale': u'linear', u'colorField': {u'type': u'integer', u'name': u'value'}, u'sizeScale': u'linear'}, u'config': {u'dataId': u'data_1', u'color': [250, 116, 0], u'textLabel': {u'color': [255, 255, 255], u'field': None, u'size': 50, u'anchor': u'middle', u'offset': [0, 0]}, u'label': u'H3 Hexagon', u'isVisible': True, u'visConfig': {u'coverageRange': [0, 1], u'opacity': 0.8, u'elevationScale': 5, u'hi-precision': False, u'coverage': 1, u'enable3d': True, u'sizeRange': [0, 500], u'colorRange': {u'category': u'Uber', u'type': u'sequential', u'colors': [u'#194266', u'#355C7D', u'#63617F', u'#916681', u'#C06C84', u'#D28389', u'#E59A8F', u'#F8B195'], u'reversed': False, u'name': u'Sunrise 8'}}, u'columns': {u'hex_id': u'hex_id'}}, u'id': u'jdys7lp'}], u'interactionConfig': {u'brush': {u'enabled': False, u'size': 0.5}, u'tooltip': {u'fieldsToShow': {u'data_1': [u'hex_id', u'value']}, u'enabled': True}}, u'splitMaps': [], u'layerBlending': u'normal', u'filters': []}, u'mapState': {u'bearing': 2.6192893401015205, u'dragRotate': True, u'zoom': 12.32053899007826, u'longitude': -122.42590232651203, u'isSplit': False, u'pitch': 37.374216241015446, u'latitude': 37.76209132041332}, u'mapStyle': {u'mapStyles': {}, u'topLayerGroups': {}, u'styleType': u'dark', u'visibleLayerGroups': {u'building': True, u'land': True, u'3d building': False, u'label': True, u'water': True, u'border': False, u'road': True}}}}