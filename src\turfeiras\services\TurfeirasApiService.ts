import { TurfeirasData, TurfeirasFeature, ApiResponse } from '../types/turfeirasTypes';

// Dados de turfeiras brasileiras integrados
const TURFEIRAS_MOCK_DATA: TurfeirasData = {
  type: "FeatureCollection",
  features: [
    {
      type: "Feature",
      properties: {
        id: 1,
        nome: "Turfeira do Pantanal Norte",
        estado: "Mato Grosso",
        municipio: "Poconé",
        area_hectares: 1250.5,
        profundidade_media: 2.8,
        ph: 4.2,
        carbono_organico: 45.8,
        tipo_vegetacao: "Gramíneas e ciperáceas",
        status_conservacao: "Preservada",
        data_coleta: "2024-03-15",
        temperatura_agua: 24.5,
        condutividade: 85.2,
        oxigenio_dissolvido: 6.8,
        especies_dominantes: "Cyperus giganteus, Echinochloa polystachya",
        ameacas: "Expansão agrícola, Mudanças climáticas",
        importancia_ecologica: "Alta - Habitat de aves aquáticas migratórias"
      },
      geometry: {
        type: "Point",
        coordinates: [-56.6269, -16.2587]
      }
    },
    {
      type: "Feature",
      properties: {
        id: 2,
        nome: "Turfeira da Serra do Cipó",
        estado: "Minas Gerais",
        municipio: "Santana do Riacho",
        area_hectares: 890.3,
        profundidade_media: 1.9,
        ph: 4.8,
        carbono_organico: 52.1,
        tipo_vegetacao: "Campo rupestre",
        status_conservacao: "Protegida - Parque Nacional",
        data_coleta: "2024-02-28",
        temperatura_agua: 18.2,
        condutividade: 42.7,
        oxigenio_dissolvido: 8.1,
        especies_dominantes: "Sphagnum sp., Drosera montana",
        ameacas: "Turismo desordenado, Queimadas",
        importancia_ecologica: "Muito Alta - Endemismo de plantas carnívoras"
      },
      geometry: {
        type: "Point",
        coordinates: [-43.5947, -19.3497]
      }
    },
    {
      type: "Feature",
      properties: {
        id: 3,
        nome: "Turfeira da Chapada dos Veadeiros",
        estado: "Goiás",
        municipio: "Alto Paraíso de Goiás",
        area_hectares: 2100.8,
        profundidade_media: 3.2,
        ph: 4.0,
        carbono_organico: 48.9,
        tipo_vegetacao: "Cerrado úmido",
        status_conservacao: "Preservada",
        data_coleta: "2024-01-20",
        temperatura_agua: 22.1,
        condutividade: 68.4,
        oxigenio_dissolvido: 7.3,
        especies_dominantes: "Mauritia flexuosa, Buriti palm",
        ameacas: "Mineração, Expansão urbana",
        importancia_ecologica: "Alta - Corredor ecológico do Cerrado"
      },
      geometry: {
        type: "Point",
        coordinates: [-47.5219, -14.1313]
      }
    },
    {
      type: "Feature",
      properties: {
        id: 4,
        nome: "Turfeira da Amazônia Central",
        estado: "Amazonas",
        municipio: "Manaus",
        area_hectares: 3450.2,
        profundidade_media: 4.1,
        ph: 3.8,
        carbono_organico: 58.7,
        tipo_vegetacao: "Floresta de várzea",
        status_conservacao: "Em risco",
        data_coleta: "2024-04-10",
        temperatura_agua: 28.9,
        condutividade: 125.6,
        oxigenio_dissolvido: 5.2,
        especies_dominantes: "Victoria amazonica, Eichhornia crassipes",
        ameacas: "Desmatamento, Poluição urbana, Mudanças climáticas",
        importancia_ecologica: "Crítica - Maior reserva de carbono da região"
      },
      geometry: {
        type: "Point",
        coordinates: [-60.0261, -3.1190]
      }
    },
    {
      type: "Feature",
      properties: {
        id: 5,
        nome: "Turfeira da Mata Atlântica",
        estado: "São Paulo",
        municipio: "Bertioga",
        area_hectares: 675.9,
        profundidade_media: 2.1,
        ph: 5.2,
        carbono_organico: 41.3,
        tipo_vegetacao: "Restinga",
        status_conservacao: "Protegida",
        data_coleta: "2024-03-05",
        temperatura_agua: 21.7,
        condutividade: 95.8,
        oxigenio_dissolvido: 7.9,
        especies_dominantes: "Typha domingensis, Eleocharis sp.",
        ameacas: "Urbanização costeira, Poluição marinha",
        importancia_ecologica: "Alta - Filtro natural para águas costeiras"
      },
      geometry: {
        type: "Point",
        coordinates: [-46.1391, -23.8536]
      }
    }
  ]
};

export class TurfeirasApiService {
  private static baseUrl = process.env.REACT_APP_API_URL || '/api';

  // Simula carregamento de dados (pode ser substituído por chamada real à API)
  static async getTurfeirasData(): Promise<TurfeirasData> {
    // Simula delay de rede
    await new Promise(resolve => setTimeout(resolve, 500));

    try {
      // Em produção, faria uma chamada real à API
      // const response = await fetch(`${this.baseUrl}/turfeiras`);
      // const data = await response.json();
      // return data;

      return TURFEIRAS_MOCK_DATA;
    } catch (error) {
      console.error('Erro ao carregar dados das turfeiras:', error);
      throw new Error('Falha ao carregar dados das turfeiras');
    }
  }

  // Método para calcular estatísticas
  static calculateStatistics(data: TurfeirasData) {
    const features = data.features;

    return {
      totalTurfeiras: features.length,
      areaTotal: features.reduce((sum, f) => sum + f.properties.area_hectares, 0),
      carbonoTotal: features.reduce((sum, f) => sum + (f.properties.carbono_organico * f.properties.area_hectares / 100), 0),
      phMedio: features.reduce((sum, f) => sum + f.properties.ph, 0) / features.length,
      profundidadeMedia: features.reduce((sum, f) => sum + f.properties.profundidade_media, 0) / features.length,
      estados: [...new Set(features.map(f => f.properties.estado))].length
    };
  }

  // Método para filtrar turfeiras por critérios
  static filterTurfeiras(data: TurfeirasData, filters: {
    estado?: string;
    statusConservacao?: string;
    areaMinima?: number;
    phRange?: [number, number];
  }): TurfeirasData {
    let filteredFeatures = data.features;

    if (filters.estado) {
      filteredFeatures = filteredFeatures.filter(f =>
        f.properties.estado.toLowerCase().includes(filters.estado!.toLowerCase())
      );
    }

    if (filters.statusConservacao) {
      filteredFeatures = filteredFeatures.filter(f =>
        f.properties.status_conservacao.toLowerCase().includes(filters.statusConservacao!.toLowerCase())
      );
    }

    if (filters.areaMinima) {
      filteredFeatures = filteredFeatures.filter(f =>
        f.properties.area_hectares >= filters.areaMinima!
      );
    }

    if (filters.phRange) {
      filteredFeatures = filteredFeatures.filter(f =>
        f.properties.ph >= filters.phRange![0] && f.properties.ph <= filters.phRange![1]
      );
    }

    return {
      type: 'FeatureCollection',
      features: filteredFeatures
    };
  }
}