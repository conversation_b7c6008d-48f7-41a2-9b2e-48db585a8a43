{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "df = pd.read_csv('hex-data.csv')\n", "df = df.fillna('')\n", "print(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "with open('sf_zip_geo.json', 'r') as f:\n", "    geojson = f.read()\n", "\n", "json_data=json.loads(geojson)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# assign config = hex_config\n", "%run hex_config.py"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import keplergl\n", "w1 = keplergl.KeplerGl(height=500, data={\"data_1\": df}, config=config)\n", "# w1 = keplergl.KeplerGl(height=500)\n", "w1"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["w1.add_data(df, 'data_1')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["w1.config"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["w1.add_data(json_data, 'geojson')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["w1.save_to_html(file_name='first_map.html', data={\"data_1\": df}, config=config)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 4}