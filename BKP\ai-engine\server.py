"""
Servidor de IA para análise geomorfológica de turfeiras tropicais - V3

Este servidor fornece endpoints para análise geomorfológica de turfeiras usando 
modelos de aprendizado de máquina e processamento de imagens.
"""

import os
import json
import logging
from flask import Flask, request, jsonify
from flask_cors import CORS
from models.random_forest.geomorph_classifier import GeomorphClassifier

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Inicializar o aplicativo Flask
app = Flask(__name__)
CORS(app)  # Habilitar CORS para permitir requisições do frontend

# Inicializar modelo de classificação geomorfológica
classifier_path = os.path.join('models', 'saved', 'geomorph_classifier.pkl')
geomorph_classifier = GeomorphClassifier(model_path=classifier_path)

@app.route('/health', methods=['GET'])
def health_check():
    """Endpoint para verificação de saúde do serviço"""
    return jsonify({
        'status': 'healthy',
        'version': '3.0.0',
        'service': 'Turfeiras Geomorphology AI Engine',
        'models_loaded': {
            'geomorph_classifier': geomorph_classifier.model is not None
        }
    })

@app.route('/classify-geomorphology', methods=['POST'])
def classify_geomorphology():
    """Endpoint para classificação geomorfológica de turfeiras"""
    try:
        data = request.get_json()
        if not data or 'geometry' not in data:
            return jsonify({'error': 'Geometria não fornecida'}), 400
        
        geometry = data['geometry']
        raster_data = data.get('raster_data', None)
        
        # Classificar geomorfologia
        result = geomorph_classifier.classify_geomorphology(geometry, raster_data)
        
        # Adicionar metadados
        result['metadata'] = {
            'timestamp': data.get('timestamp'),
            'model_version': '3.0.0',
            'processing_time': 0.5,  # simulado
            'data_source': 'Satellite Imagery + DEM'
        }
        
        return jsonify(result)
    
    except Exception as e:
        logger.error(f"Erro ao classificar geomorfologia: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/feature-importance', methods=['GET'])
def get_feature_importance():
    """Endpoint para obter importância das características do modelo"""
    try:
        importance = geomorph_classifier.get_feature_importance()
        
        if not importance:
            return jsonify({'error': 'Modelo não treinado ou não suporta importância de características'}), 400
        
        # Ordenar por importância
        sorted_importance = sorted(importance.items(), key=lambda x: x[1], reverse=True)
        
        return jsonify({
            'feature_importance': dict(sorted_importance),
            'model_type': 'Random Forest',
            'total_features': len(importance)
        })
    
    except Exception as e:
        logger.error(f"Erro ao obter importância das características: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/geomorphology-types', methods=['GET'])
def get_geomorphology_types():
    """Endpoint para obter tipos de geomorfologia suportados"""
    try:
        types_info = {}
        
        for class_name in geomorph_classifier.classes:
            types_info[class_name] = {
                'description': geomorph_classifier._get_geomorphology_description(class_name),
                'characteristics': geomorph_classifier._get_characteristics(class_name)
            }
        
        return jsonify({
            'geomorphology_types': types_info,
            'total_classes': len(geomorph_classifier.classes)
        })
    
    except Exception as e:
        logger.error(f"Erro ao obter tipos de geomorfologia: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/batch-classify', methods=['POST'])
def batch_classify():
    """Endpoint para classificação em lote de múltiplas geometrias"""
    try:
        data = request.get_json()
        if not data or 'geometries' not in data:
            return jsonify({'error': 'Lista de geometrias não fornecida'}), 400
        
        geometries = data['geometries']
        if not isinstance(geometries, list):
            return jsonify({'error': 'Geometrias deve ser uma lista'}), 400
        
        results = []
        for i, geometry in enumerate(geometries):
            try:
                result = geomorph_classifier.classify_geomorphology(geometry)
                result['geometry_id'] = i
                results.append(result)
            except Exception as e:
                logger.error(f"Erro ao processar geometria {i}: {e}")
                results.append({
                    'geometry_id': i,
                    'error': str(e),
                    'class': 'Desconhecido',
                    'confidence': 0.0
                })
        
        return jsonify({
            'results': results,
            'total_processed': len(results),
            'successful': len([r for r in results if 'error' not in r]),
            'failed': len([r for r in results if 'error' in r])
        })
    
    except Exception as e:
        logger.error(f"Erro na classificação em lote: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/model-info', methods=['GET'])
def get_model_info():
    """Endpoint para obter informações do modelo"""
    try:
        return jsonify({
            'model_type': 'Random Forest Classifier',
            'version': '3.0.0',
            'features': geomorph_classifier.features,
            'classes': geomorph_classifier.classes,
            'is_trained': geomorph_classifier.model is not None,
            'description': 'Classificador geomorfológico para turfeiras tropicais'
        })
    
    except Exception as e:
        logger.error(f"Erro ao obter informações do modelo: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/predict', methods=['POST'])
def predict():
    """Endpoint genérico para predição com características fornecidas"""
    try:
        data = request.get_json()
        if not data or 'features' not in data:
            return jsonify({'error': 'Características não fornecidas'}), 400
        
        features = data['features']
        if not isinstance(features, list):
            return jsonify({'error': 'Características devem ser uma lista'}), 400
        
        # Converter para numpy array
        import numpy as np
        features_array = np.array(features).reshape(1, -1)
        
        # Fazer predição
        predictions, probabilities = geomorph_classifier.predict(features_array)
        
        if predictions is None:
            return jsonify({'error': 'Modelo não treinado'}), 400
        
        # Criar mapeamento de classes para probabilidades
        class_probabilities = {}
        for i, class_name in enumerate(geomorph_classifier.classes):
            class_probabilities[class_name] = float(probabilities[0][i])
        
        return jsonify({
            'predicted_class': predictions[0],
            'confidence': float(np.max(probabilities[0])),
            'probabilities': class_probabilities
        })
    
    except Exception as e:
        logger.error(f"Erro na predição: {e}")
        return jsonify({'error': str(e)}), 500

# Tratamento de erros globais
@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint não encontrado'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Erro interno do servidor'}), 500

if __name__ == '__main__':
    logger.info("Iniciando servidor de IA para análise geomorfológica de turfeiras...")
    
    # Verificar se o modelo está carregado
    if geomorph_classifier.model is None:
        logger.warning("Modelo não carregado. Usando modelo padrão não treinado.")
    else:
        logger.info("Modelo geomorfológico carregado com sucesso.")
    
    # Iniciar servidor Flask
    app.run(
        host='0.0.0.0',
        port=int(os.environ.get('PORT', 5000)),
        debug=os.environ.get('DEBUG', 'False').lower() == 'true'
    )
