version: '3.8'

services:
  kepler-demo:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - MAPBOX_ACCESS_TOKEN=pk.demo
    container_name: kepler-turfeiras-v4
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080"]
      interval: 30s
      timeout: 10s
      retries: 3
