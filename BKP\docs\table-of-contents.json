{"id": "table-of-contents", "chapters": [{"title": "User Guides", "chapters": [{"title": "Overview", "entries": [{"entry": "docs"}, {"entry": "docs/user-guides/a-introduction"}, {"entry": "docs/user-guides/b-kepler-gl-workflow/a-add-data-to-the-map"}, {"entry": "docs/user-guides/c-types-of-layers"}, {"entry": "docs/user-guides/d-layer-attributes"}, {"entry": "docs/user-guides/e-filters"}, {"entry": "docs/user-guides/f-map-styles"}, {"entry": "docs/user-guides/g-interactions"}, {"entry": "docs/user-guides/h-playback"}, {"entry": "docs/user-guides/i-FAQ"}, {"entry": "docs/user-guides/j-get-started"}, {"entry": "docs/user-guides/k-save-and-export"}, {"entry": "docs/user-guides/l-color-attributes"}, {"entry": "docs/user-guides/m-map-settings"}]}, {"title": "Workflow", "entries": [{"entry": "docs/user-guides/b-kepler-gl-workflow/b-add-data-layers/a-adding-data-layers"}, {"entry": "docs/user-guides/b-kepler-gl-workflow/b-add-data-layers/b-create-a-layer"}, {"entry": "docs/user-guides/b-kepler-gl-workflow/b-add-data-layers/c-hide-edit-and-delete-layers"}, {"entry": "docs/user-guides/b-kepler-gl-workflow/b-add-data-layers/d-blend-and-rearrange-layers"}]}, {"title": "Layer Types", "entries": [{"entry": "docs/user-guides/c-types-of-layers/a-point"}, {"entry": "docs/user-guides/c-types-of-layers/b-arc"}, {"entry": "docs/user-guides/c-types-of-layers/c-line"}, {"entry": "docs/user-guides/c-types-of-layers/d-grid"}, {"entry": "docs/user-guides/c-types-of-layers/e-polygon"}, {"entry": "docs/user-guides/c-types-of-layers/f-cluster"}, {"entry": "docs/user-guides/c-types-of-layers/g-icon"}, {"entry": "docs/user-guides/c-types-of-layers/h-hexbin"}, {"entry": "docs/user-guides/c-types-of-layers/i-heatmap"}, {"entry": "docs/user-guides/c-types-of-layers/j-h3"}, {"entry": "docs/user-guides/c-types-of-layers/k.trip"}]}]}, {"title": "API Reference", "chapters": [{"title": "Overview", "entries": [{"entry": "docs/api-reference/overview"}]}, {"title": "Actions", "entries": [{"entry": "docs/api-reference/actions/overview"}, {"entry": "docs/api-reference/actions/actions"}]}, {"title": "Components", "entries": [{"entry": "docs/api-reference/components/overview"}]}, {"title": "Processors", "entries": [{"entry": "docs/api-reference/processors/overview"}, {"entry": "docs/api-reference/processors/processors"}]}, {"title": "Reducers", "entries": [{"entry": "docs/api-reference/reducers/overview"}, {"entry": "docs/api-reference/reducers/combine"}, {"entry": "docs/api-reference/reducers/map-state"}, {"entry": "docs/api-reference/reducers/map-style"}, {"entry": "docs/api-reference/reducers/reducers"}, {"entry": "docs/api-reference/reducers/ui-state"}, {"entry": "docs/api-reference/reducers/vis-state"}]}, {"title": "<PERSON><PERSON><PERSON>", "entries": [{"entry": "docs/api-reference/schemas/overview"}]}, {"title": "Advanced Usage", "entries": [{"entry": "docs/api-reference/advanced-usages/custom-initial-state"}, {"entry": "docs/api-reference/advanced-usages/custom-mapbox-host"}, {"entry": "docs/api-reference/advanced-usages/forward-actions"}, {"entry": "docs/api-reference/advanced-usages/reducer-plugin"}, {"entry": "docs/api-reference/advanced-usages/replace-ui-component"}, {"entry": "docs/api-reference/advanced-usages/saving-loading-w-schema"}, {"entry": "docs/api-reference/advanced-usages/using-updaters"}]}]}, {"title": "kepler.gl-jupyter", "entries": [{"entry": "docs/keplergl-jupyter/user-guide"}]}]}