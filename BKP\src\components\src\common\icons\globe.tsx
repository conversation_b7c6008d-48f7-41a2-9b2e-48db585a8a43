// SPDX-License-Identifier: MIT
// Copyright contributors to the kepler.gl project

import React, {Component} from 'react';
import PropTypes from 'prop-types';
import Base, {BaseProps} from './base';

export default class Globe extends Component<Partial<BaseProps>> {
  static propTypes = {
    /** Set the height of the icon, ex. '16px' */
    height: PropTypes.string
  };

  static defaultProps = {
    height: '16px',
    viewBox: '0 0 16 16',
    predefinedClassName: 'data-ex-icons-globe'
  };

  render() {
    return (
      <Base {...this.props}>
        <svg width="16" height="17" viewBox="0 0 16 17" xmlns="http://www.w3.org/2000/svg">
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M6.38584 1.68702C6.90402 1.56472 7.44445 1.5 8 1.5C9.23055 1.5 10.3869 1.81752 11.3917 2.37511C12.4112 2.94093 13.2749 3.75396 13.9013 4.73321C14.4377 5.5719 14.6851 6.188 14.7456 6.62353C14.9114 7.22072 15 7.85001 15 8.5C15 10.527 14.1385 12.3527 12.7616 13.631C11.9832 14.3623 10.9811 14.9754 9.75268 15.2788C9.19254 15.4232 8.60524 15.5 8 15.5C4.63461 15.5 1.82391 13.1251 1.15243 9.95976C1.05442 9.54484 1 9.06056 1 8.5C1 8.49999 1 8.50001 1 8.5C1 5.19522 3.29014 2.42529 6.36999 1.69074C6.37528 1.68948 6.38056 1.68824 6.38584 1.68702ZM14 8.5C14 9.08044 13.9176 9.64163 13.7638 10.1725C12.755 9.00544 10.8189 8.33275 9.51221 8.62684C7.80468 9.01112 7.72976 10.9923 8.46642 12.0631C8.83704 12.6018 8.91074 13.1143 8.98743 13.6476C9.02242 13.891 9.05804 14.1387 9.12278 14.3951C8.75907 14.464 8.38374 14.5 8 14.5C6.01503 14.5 4.2552 13.5361 3.16302 12.0508C3.96089 12.1461 4.82827 11.8298 5.48008 11.2844C6.69976 10.2638 6.57435 9.20516 6.44788 8.13751C6.40898 7.80916 6.36999 7.47997 6.36999 7.15077C6.36999 6.39461 6.69305 5.95645 7.06097 5.45747C7.37404 5.03287 7.71959 4.56422 7.92621 3.81811C8.07377 3.28525 8.08049 2.84344 7.9823 2.50003C7.9882 2.50001 7.9941 2.5 8 2.5C8.67572 2.5 9.32534 2.6117 9.93142 2.81764C9.29673 3.3956 9.1115 4.40569 9.43855 5.14271C9.79682 5.95012 10.3908 6.13003 10.9903 6.31161C11.2417 6.38774 11.494 6.46415 11.7303 6.58722C12.5302 7.00383 13.1339 7.29592 13.9013 7.47784L13.9138 7.48073C13.9705 7.81201 14 8.15256 14 8.5ZM6.62925 2.65729C6.77252 2.62674 6.8659 2.63612 6.91413 2.64841C6.95864 2.65974 6.97132 2.67386 6.97807 2.68199C6.98858 2.69464 7.1408 2.9073 6.96248 3.55123C6.80646 4.11462 6.56084 4.44896 6.24765 4.87526L6.17228 4.97803C5.80911 5.47481 5.36999 6.12055 5.36999 7.15077C5.36999 7.54169 5.41575 7.92659 5.45353 8.24443L5.46209 8.31662C5.50393 8.67118 5.53234 8.9484 5.52363 9.2086C5.50906 9.64448 5.39669 10.0503 4.83835 10.5175C4.22693 11.0291 3.48249 11.1744 2.9997 10.9895C2.7365 10.8887 2.35103 10.6037 2.14406 9.81283C2.04975 9.3903 2 8.95096 2 8.5C2 8.49353 2.00001 8.48706 2.00003 8.48059C2.00034 8.38358 2.00295 8.28711 2.00781 8.19124C2.1449 5.48676 4.07275 3.25474 6.62925 2.65729ZM13.3119 11.2912C13.0234 11.834 12.6167 12.3926 12.0926 12.8876C11.5179 13.4239 10.8378 13.8485 10.0867 14.1272C10.0403 13.9356 10.0098 13.7315 9.97582 13.4953L9.97119 13.4631C9.89588 12.9383 9.79404 12.2285 9.29027 11.4963C9.07472 11.183 8.96775 10.6974 9.05201 10.2901C9.12368 9.94368 9.31182 9.69695 9.73177 9.60244C10.2869 9.47751 11.1638 9.61414 11.9811 10.0425C12.6583 10.3974 13.1122 10.8535 13.3119 11.2912ZM11.1022 3.36315C12.235 4.04873 13.1228 5.09845 13.6033 6.35005C13.1774 6.20083 12.7565 5.99418 12.1922 5.70029C11.9064 5.55147 11.6211 5.45906 11.4003 5.39112C11.351 5.37595 11.3054 5.36211 11.2629 5.34919C11.0939 5.29784 10.9724 5.26095 10.8505 5.21158C10.622 5.11901 10.4782 5.02021 10.3526 4.73711C10.2389 4.48099 10.2524 4.0818 10.4376 3.76614C10.5684 3.5432 10.7707 3.37786 11.1022 3.36315Z"
          />
        </svg>
      </Base>
    );
  }
}
