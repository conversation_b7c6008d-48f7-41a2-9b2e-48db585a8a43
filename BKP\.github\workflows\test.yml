# This workflow will do a clean install of node dependencies, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Node.js CI

on:
  push:
    branches: [master]
  pull_request:
    branches: [master]

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x]

    steps:
      - uses: actions/checkout@v4
      # use Volta to manage yarn/node versions
      - uses: volta-cli/action@v4

      - name: Install XVFB
        run: sudo apt-get install xvfb

      - name: Install Dependencies
        run: yarn install && yarn bootstrap

      # - name: Install Puppeteer
      #   run: yarn dlx "puppeteer@23.1.0"

      - name: Lint
        run: yarn lint

      - name: Test
        run: xvfb-run --auto-servernum yarn cover

      - name: <PERSON><PERSON>s
        uses: coverallsapp/github-action@master
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
