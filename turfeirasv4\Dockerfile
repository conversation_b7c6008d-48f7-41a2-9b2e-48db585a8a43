# Dockerfile para Kepler.gl Demo Limpo - Método simples que funciona
FROM node:20-alpine

LABEL maintainer="TurfeirasBrasil <<EMAIL>>"

RUN apk update && apk upgrade && apk add git

# Usar arquivos locais ao invés de git clone
WORKDIR /kepler.gl

# Copiar apenas o necessário: package.json da raiz e examples/demo-app
COPY package.json yarn.lock .yarnrc.yml ./
COPY examples/demo-app ./examples/demo-app

WORKDIR /kepler.gl/examples/demo-app

RUN npm install npm -g

RUN npm install --legacy-peer-deps --save kepler.gl

# Modificar o comando start para usar esbuild sem abrir browser
RUN sed -i 's/"start": ".*"/"start": "NODE_OPTIONS=--openssl-legacy-provider node esbuild.config.mjs --start --port 8080 --host 0.0.0.0"/' package.json

<PERSON> ["npm", "start"]

EXPOSE 8080
