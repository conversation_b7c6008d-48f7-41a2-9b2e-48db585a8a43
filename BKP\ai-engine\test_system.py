"""
Testes para o sistema de classificação geomorfológica de turfeiras - V3
"""

import os
import sys
import json
import unittest
from unittest.mock import patch

# Adicionar o diretório pai ao path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.random_forest.geomorph_classifier import GeomorphClassifier

class TestGeomorphClassifier(unittest.TestCase):
    """Testes para o classificador geomorfológico"""
    
    def setUp(self):
        """Configurar testes"""
        self.classifier = GeomorphClassifier()
        self.test_geometry = {
            "type": "Polygon",
            "coordinates": [[
                [-45.0, -22.0],
                [-45.0, -22.1],
                [-45.1, -22.1],
                [-45.1, -22.0],
                [-45.0, -22.0]
            ]]
        }
    
    def test_classifier_initialization(self):
        """Testar inicialização do classificador"""
        self.assertIsNotNone(self.classifier)
        self.assertIsNotNone(self.classifier.model)
        self.assertEqual(len(self.classifier.features), 20)
        self.assertEqual(len(self.classifier.classes), 7)
    
    def test_classify_geomorphology(self):
        """Testar classificação geomorfológica"""
        result = self.classifier.classify_geomorphology(self.test_geometry)
        
        self.assertIn('class', result)
        self.assertIn('confidence', result)
        self.assertIn('probabilities', result)
        self.assertIn('geomorphology_type', result)
        self.assertIn('characteristics', result)
        
        # Verificar se a classe está entre as classes válidas
        self.assertIn(result['class'], self.classifier.classes)
        
        # Verificar se a confiança está entre 0 e 1
        self.assertGreaterEqual(result['confidence'], 0.0)
        self.assertLessEqual(result['confidence'], 1.0)
        
        # Verificar se as probabilidades somam 1
        prob_sum = sum(result['probabilities'].values())
        self.assertAlmostEqual(prob_sum, 1.0, places=5)
    
    def test_extract_features(self):
        """Testar extração de características"""
        features = self.classifier._extract_features(self.test_geometry)
        
        self.assertIsNotNone(features)
        self.assertEqual(features.shape[0], 1)
        self.assertEqual(features.shape[1], len(self.classifier.features))
    
    def test_get_feature_importance(self):
        """Testar obtenção de importância das características"""
        importance = self.classifier.get_feature_importance()
        
        self.assertIsInstance(importance, dict)
        self.assertEqual(len(importance), len(self.classifier.features))
        
        # Verificar se todas as importâncias são não negativas
        for imp in importance.values():
            self.assertGreaterEqual(imp, 0.0)
    
    def test_geomorphology_descriptions(self):
        """Testar descrições das classes geomorfológicas"""
        for class_name in self.classifier.classes:
            description = self.classifier._get_geomorphology_description(class_name)
            self.assertIsInstance(description, str)
            self.assertGreater(len(description), 0)
    
    def test_characteristics(self):
        """Testar características das classes"""
        for class_name in self.classifier.classes:
            characteristics = self.classifier._get_characteristics(class_name)
            self.assertIsInstance(characteristics, dict)
            
            # Verificar se as características esperadas estão presentes
            expected_keys = ['slope_range', 'drainage', 'water_retention', 'carbon_stock', 'vulnerability']
            for key in expected_keys:
                self.assertIn(key, characteristics)
    
    def test_invalid_geometry(self):
        """Testar com geometria inválida"""
        invalid_geometry = {"type": "Point", "coordinates": [0, 0]}
        
        result = self.classifier.classify_geomorphology(invalid_geometry)
        
        # Deve retornar resultado mesmo com geometria inválida (usando dados simulados)
        self.assertIn('class', result)
        self.assertIn('confidence', result)
    
    def test_model_save_load(self):
        """Testar salvamento e carregamento do modelo"""
        import tempfile
        import shutil
        
        # Criar diretório temporário
        temp_dir = tempfile.mkdtemp()
        
        try:
            model_path = os.path.join(temp_dir, 'test_model.pkl')
            
            # Salvar modelo
            self.classifier.save_model(model_path)
            self.assertTrue(os.path.exists(model_path))
            
            # Carregar modelo
            new_classifier = GeomorphClassifier(model_path=model_path)
            self.assertIsNotNone(new_classifier.model)
            
        finally:
            # Limpar diretório temporário
            shutil.rmtree(temp_dir)


class TestServerIntegration(unittest.TestCase):
    """Testes de integração com o servidor"""
    
    def setUp(self):
        """Configurar testes do servidor"""
        # Importar aqui para evitar problemas se o Flask não estiver instalado
        try:
            from server import app
            self.app = app.test_client()
        except ImportError:
            self.skipTest("Flask não está instalado")
    
    def test_health_endpoint(self):
        """Testar endpoint de saúde"""
        response = self.app.get('/health')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'healthy')
        self.assertIn('models_loaded', data)
    
    def test_classify_endpoint(self):
        """Testar endpoint de classificação"""
        test_data = {
            'geometry': {
                "type": "Polygon",
                "coordinates": [[
                    [-45.0, -22.0],
                    [-45.0, -22.1],
                    [-45.1, -22.1],
                    [-45.1, -22.0],
                    [-45.0, -22.0]
                ]]
            }
        }
        
        response = self.app.post('/classify-geomorphology', 
                                json=test_data,
                                content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertIn('class', data)
        self.assertIn('confidence', data)
        self.assertIn('metadata', data)
    
    def test_geomorphology_types_endpoint(self):
        """Testar endpoint de tipos de geomorfologia"""
        response = self.app.get('/geomorphology-types')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertIn('geomorphology_types', data)
        self.assertIn('total_classes', data)
    
    def test_model_info_endpoint(self):
        """Testar endpoint de informações do modelo"""
        response = self.app.get('/model-info')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertIn('model_type', data)
        self.assertIn('features', data)
        self.assertIn('classes', data)


def run_performance_test():
    """Executar teste de performance"""
    print("=== Teste de Performance ===")
    
    import time
    
    classifier = GeomorphClassifier()
    
    test_geometry = {
        "type": "Polygon",
        "coordinates": [[
            [-45.0, -22.0],
            [-45.0, -22.1],
            [-45.1, -22.1],
            [-45.1, -22.0],
            [-45.0, -22.0]
        ]]
    }
    
    # Teste de classificação única
    start_time = time.time()
    result = classifier.classify_geomorphology(test_geometry)
    single_time = time.time() - start_time
    
    print(f"Classificação única: {single_time:.4f}s")
    print(f"Classe: {result['class']}")
    print(f"Confiança: {result['confidence']:.2f}")
    
    # Teste de classificação em lote
    num_tests = 100
    start_time = time.time()
    
    for _ in range(num_tests):
        classifier.classify_geomorphology(test_geometry)
    
    batch_time = time.time() - start_time
    avg_time = batch_time / num_tests
    
    print(f"\nClassificação em lote ({num_tests} amostras):")
    print(f"Tempo total: {batch_time:.4f}s")
    print(f"Tempo médio: {avg_time:.4f}s")
    print(f"Throughput: {num_tests/batch_time:.2f} classificações/s")


if __name__ == '__main__':
    print("=== Executando Testes do Sistema Turfeiras V3 ===")
    
    # Executar testes unitários
    print("\n1. Testes Unitários:")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Executar teste de performance
    print("\n2. Teste de Performance:")
    run_performance_test()
    
    print("\n=== Testes Concluídos ===")
