<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

### Table of Contents

- [uiStateUpdaters](#uistateupdaters)
  - [addNotificationUpdater](#addnotificationupdater)
  - [cleanupExportImage](#cleanupexportimage)
  - [DEFAULT\_EXPORT\_DATA](#default_export_data)
    - [Properties](#properties)
  - [DEFAULT\_EXPORT\_IMAGE](#default_export_image)
    - [Properties](#properties-1)
  - [DEFAULT\_MAP\_CONTROLS\_FEATURES](#default_map_controls_features)
    - [Properties](#properties-2)
  - [hideExportDropdownUpdater](#hideexportdropdownupdater)
  - [INITIAL\_UI\_STATE](#initial_ui_state)
    - [Properties](#properties-3)
  - [loadFilesErrUpdater](#loadfileserrupdater)
  - [loadFilesUpdater](#loadfilesupdater)
  - [openDeleteModalUpdater](#opendeletemodalupdater)
  - [removeNotificationUpdater](#removenotificationupdater)
  - [setExportDataTypeUpdater](#setexportdatatypeupdater)
  - [setExportDataUpdater](#setexportdataupdater)
  - [setExportFilteredUpdater](#setexportfilteredupdater)
  - [setExportImageDataUri](#setexportimagedatauri)
  - [setExportImageSetting](#setexportimagesetting)
  - [setExportSelectedDatasetUpdater](#setexportselecteddatasetupdater)
  - [showExportDropdownUpdater](#showexportdropdownupdater)
  - [startExportingImage](#startexportingimage)
  - [toggleMapControlUpdater](#togglemapcontrolupdater)
  - [toggleModalUpdater](#togglemodalupdater)
  - [toggleSidePanelUpdater](#togglesidepanelupdater)
  - [toggleSplitMapUpdater](#togglesplitmapupdater)
- [DEFAULT\_EXPORT\_HTML](#default_export_html)
  - [Properties](#properties-4)
- [setUserMapboxAccessTokenUpdater](#setusermapboxaccesstokenupdater)

## uiStateUpdaters

Updaters for `uiState` reducer. Can be used in your root reducer to directly modify kepler.gl's state.
Read more about [Using updaters][53]

**Examples**

```javascript
import keplerGlReducer, {uiStateUpdaters} from '@kepler.gl/reducers';
// Root Reducer
const reducers = combineReducers({
 keplerGl: keplerGlReducer,
 app: appReducer
});

const composedReducer = (state, action) => {
 switch (action.type) {
   // click button to close side panel
   case 'CLICK_BUTTON':
     return {
       ...state,
       keplerGl: {
         ...state.keplerGl,
         foo: {
            ...state.keplerGl.foo,
            uiState: uiStateUpdaters.toggleSidePanelUpdater(
              uiState, {payload: null}
            )
         }
       }
     };
 }
 return reducers(state, action);
};

export default composedReducer;
```

### addNotificationUpdater

Add a notification to be displayed.
Existing notification is going to be updated in case of matching ids.

-   **Action**: [`addNotification`][54]

**Parameters**

-   `state` **[Object][55]** `uiState`
-   `action` **[Object][55]**
    -   `action.payload` **[Object][55]**

Returns **[Object][55]** nextState

### cleanupExportImage

Delete cached export image

-   **Action**: [`cleanupExportImage`][56]

**Parameters**

-   `state` **[Object][55]** `uiState`

Returns **[Object][55]** nextState

### DEFAULT_EXPORT_DATA

Default initial `exportData` settings

Type: [Object][55]

#### Properties

-   `selectedDataset` **[string][57]** Default: `''`,
-   `dataType` **[string][57]** Default: `'csv'`,
-   `filtered` **[boolean][58]** Default: `true`,
-   `config` **[boolean][58]** deprecated
-   `data` **[boolean][58]** used in modal config export. Default: `false`

### DEFAULT_EXPORT_IMAGE

Default image export config

Type: [Object][55]

#### Properties

-   `ratio` **[string][57]** Default: `'SCREEN'`,
-   `resolution` **[string][57]** Default: `'ONE_X'`,
-   `legend` **[boolean][58]** Default: `false`,
-   `imageDataUri` **[string][57]** Default: `''`,
-   `exporting` **[boolean][58]** Default: `false`
-   `error` **[boolean][58]** Default: `false`

### DEFAULT_MAP_CONTROLS_FEATURES

A list of map control visibility and whether is it active.

Type: [Object][55]

#### Properties

-   `visibleLayers` **[Object][55]** Default: `{show: true, active: false}`
-   `mapLegend` **[Object][55]** Default: `{show: true, active: false}`
-   `toggle3d` **[Object][55]** Default: `{show: true}`
-   `splitMap` **[Object][55]** Default: `{show: true}`

### hideExportDropdownUpdater

Hide side panel header dropdown, activated by clicking the share link on top of the side panel

-   **Action**: [`hideExportDropdown`][59]

**Parameters**

-   `state` **[Object][55]** `uiState`

Returns **[Object][55]** nextState

### INITIAL_UI_STATE

Default initial `uiState`

Type: [Object][55]

#### Properties

-   `readOnly` **[boolean][58]** Default: `false`
-   `activeSidePanel` **[string][57]** Default: `'layer'`
-   `currentModal` **([string][57] | null)** Default: `'addData'`
-   `datasetKeyToRemove` **([string][57] | null)** Default: `null`
-   `visibleDropdown` **([string][57] | null)** Default: `null`
-   `exportImage` **[Object][55]** Default: [`DEFAULT_EXPORT_IMAGE`][9]
-   `exportData` **[Object][55]** Default: [`DEFAULT_EXPORT_DATA`][7]
-   `mapControls` **[Object][55]** Default: [`DEFAULT_MAP_CONTROLS`][60]
-   `activeMapIndex` **[number][61]** defines which map the user clicked on. Default: 0

### loadFilesErrUpdater

Handles load file error and set fileLoading property to false

-   **Action**: [`loadFilesErr`][62]

**Parameters**

-   `state`
-   `error` **[Object][55]**
    -   `error.error`

Returns **[Object][55]** nextState

### loadFilesUpdater

Fired when file loading begin

-   **Action**: [`loadFiles`][63]

**Parameters**

-   `state` **[Object][55]** `uiState`

Returns **[Object][55]** nextState

### openDeleteModalUpdater

Toggle active map control panel

-   **Action**: [`openDeleteModal`][64]

**Parameters**

-   `state` **[Object][55]** `uiState`
-   `action` **[Object][55]**
    -   `action.payload` **[string][57]** dataset id

Returns **[Object][55]** nextState

### removeNotificationUpdater

Remove a notification

-   **Action**: [`removeNotification`][65]

**Parameters**

-   `state` **[Object][55]** `uiState`
-   `action` **[Object][55]**
    -   `action.payload` **[String][57]** id of the notification to be removed

Returns **[Object][55]** nextState

### setExportDataTypeUpdater

Set data format for exporting data

-   **Action**: [`setExportDataType`][66]

**Parameters**

-   `state` **[Object][55]** `uiState`
-   `action` **[Object][55]**
    -   `action.payload` **[string][57]** one of `'text/csv'`

Returns **[Object][55]** nextState

### setExportDataUpdater

Whether to including data in map config, toggle between `true` or `false`

-   **Action**: [`setExportData`][67]

**Parameters**

-   `state` **[Object][55]** `uiState`

Returns **[Object][55]** nextState

### setExportFilteredUpdater

Whether to export filtered data, `true` or `false`

-   **Action**: [`setExportFiltered`][68]

**Parameters**

-   `state` **[Object][55]** `uiState`
-   `action` **[Object][55]**
    -   `action.payload` **[boolean][58]**

Returns **[Object][55]** nextState

### setExportImageDataUri

Set `exportImage.setExportImageDataUri` to a image dataUri

-   **Action**: [`setExportImageDataUri`][69]

**Parameters**

-   `state` **[Object][55]** `uiState`
-   `action` **[Object][55]**
    -   `action.payload` **[string][57]** export image data uri

Returns **[Object][55]** nextState

### setExportImageSetting

Set `exportImage.legend` to `true` or `false`

-   **Action**: [`setExportImageSetting`][70]

**Parameters**

-   `state` **[Object][55]** `uiState`
-   `$1` **[Object][55]**
    -   `$1.payload`

Returns **[Object][55]** nextState

### setExportSelectedDatasetUpdater

Set selected dataset for export

-   **Action**: [`setExportSelectedDataset`][71]

**Parameters**

-   `state` **[Object][55]** `uiState`
-   `action` **[Object][55]**
    -   `action.payload` **[string][57]** dataset id

Returns **[Object][55]** nextState

### showExportDropdownUpdater

Hide and show side panel header dropdown, activated by clicking the share link on top of the side panel

-   **Action**: [`showExportDropdown`][72]

**Parameters**

-   `state` **[Object][55]** `uiState`
-   `action` **[Object][55]**
    -   `action.payload` **[string][57]** id of the dropdown

Returns **[Object][55]** nextState

### startExportingImage

Set `exportImage.exporting` to `true`

-   **Action**: [`startExportingImage`][73]

**Parameters**

-   `state` **[Object][55]** `uiState`

Returns **[Object][55]** nextState

### toggleMapControlUpdater

Toggle active map control panel

-   **Action**: [`toggleMapControl`][74]

**Parameters**

-   `state` **[Object][55]** `uiState`
-   `action` **[Object][55]** action
    -   `action.payload` **[string][57]** map control panel id, one of the keys of: [`DEFAULT_MAP_CONTROLS`][60]
    -   `action.payload.panelId`
    -   `action.payload.index`   (optional, default `0`)

Returns **[Object][55]** nextState

### toggleModalUpdater

Show and hide modal dialog

-   **Action**: [`toggleModal`][75]

**Parameters**

-   `state` **[Object][55]** `uiState`
-   `action` **[Object][55]**
    -   `action.payload` **([string][57] | null)** id of modal to be shown, null to hide modals. One of:-   [`DATA_TABLE_ID`][76]
        -   [`DELETE_DATA_ID`][77]
        -   [`ADD_DATA_ID`][78]
        -   [`EXPORT_IMAGE_ID`][79]
        -   [`EXPORT_DATA_ID`][80]
        -   [`ADD_MAP_STYLE_ID`][81]

Returns **[Object][55]** nextState

### toggleSidePanelUpdater

Toggle active side panel

-   **Action**: [`toggleSidePanel`][82]

**Parameters**

-   `state` **[Object][55]** `uiState`
-   `action` **[Object][55]**
    -   `action.payload` **([string][57] | null)** id of side panel to be shown, one of `layer`, `filter`, `interaction`, `map`. close side panel if `null`

Returns **[Object][55]** nextState

### toggleSplitMapUpdater

Handles toggle map split and reset all map control index to 0

-   **Action**: [`toggleSplitMap`][83]

**Parameters**

-   `state`

Returns **[Object][55]** nextState

## DEFAULT_EXPORT_HTML

Type: [Object][55]

### Properties

-   `exportMapboxAccessToken` **[string][57]** Default: null, this is used when we provide a default mapbox token for users to take advantage of
-   `userMapboxToken` **[string][57]** Default: '', mapbox token provided by user through input field

## setUserMapboxAccessTokenUpdater

whether to export a mapbox access to HTML single page

-   **Action**: [`setUserMapboxAccessToken`][84]

**Parameters**

-   `state` **[Object][55]** `uiState`
-   `action` **[Object][55]**
    -   `action.payload` **[string][57]**

Returns **[Object][55]** nextState

[1]: #uistateupdaters

[2]: #examples

[3]: #addnotificationupdater

[4]: #parameters

[5]: #cleanupexportimage

[6]: #parameters-1

[7]: #default_export_data

[8]: #properties

[9]: #default_export_image

[10]: #properties-1

[11]: #default_map_controls_features

[12]: #properties-2

[13]: #hideexportdropdownupdater

[14]: #parameters-2

[15]: #initial_ui_state

[16]: #properties-3

[17]: #loadfileserrupdater

[18]: #parameters-3

[19]: #loadfilesupdater

[20]: #parameters-4

[21]: #opendeletemodalupdater

[22]: #parameters-5

[23]: #removenotificationupdater

[24]: #parameters-6

[25]: #setexportdatatypeupdater

[26]: #parameters-7

[27]: #setexportdataupdater

[28]: #parameters-8

[29]: #setexportfilteredupdater

[30]: #parameters-9

[31]: #setexportimagedatauri

[32]: #parameters-10

[33]: #setexportimagesetting

[34]: #parameters-11

[35]: #setexportselecteddatasetupdater

[36]: #parameters-12

[37]: #showexportdropdownupdater

[38]: #parameters-13

[39]: #startexportingimage

[40]: #parameters-14

[41]: #togglemapcontrolupdater

[42]: #parameters-15

[43]: #togglemodalupdater

[44]: #parameters-16

[45]: #togglesidepanelupdater

[46]: #parameters-17

[47]: #togglesplitmapupdater

[48]: #parameters-18

[49]: #default_export_html

[50]: #properties-4

[51]: #setusermapboxaccesstokenupdater

[52]: #parameters-19

[53]: ../advanced-usage/using-updaters.md

[54]: ../actions/actions.md#addnotification

[55]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[56]: ../actions/actions.md#cleanupexportimage

[57]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[58]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

[59]: ../actions/actions.md#hideexportdropdown

[60]: #default_map_controls

[61]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[62]: ../actions/actions.md#loadfileserr

[63]: ../actions/actions.md#loadfiles

[64]: ../actions/actions.md#opendeletemodal

[65]: ../actions/actions.md#removenotification

[66]: ../actions/actions.md#setexportdatatype

[67]: ../actions/actions.md#setexportdata

[68]: ../actions/actions.md#setexportfiltered

[69]: ../actions/actions.md#setexportimagedatauri

[70]: ../actions/actions.md#setexportimagesetting

[71]: ../actions/actions.md#setexportselecteddataset

[72]: ../actions/actions.md#showexportdropdown

[73]: ../actions/actions.md#startexportingimage

[74]: ../actions/actions.md#togglemapcontrol

[75]: ../actions/actions.md#togglemodal

[76]: ../constants/default-settings.md#data_table_id

[77]: ../constants/default-settings.md#delete_data_id

[78]: ../constants/default-settings.md#add_data_id

[79]: ../constants/default-settings.md#export_image_id

[80]: ../constants/default-settings.md#export_data_id

[81]: ../constants/default-settings.md#add_map_style_id

[82]: ../actions/actions.md#togglesidepanel

[83]: ../actions/actions.md#togglesplitmap

[84]: ../actions/actions.md#setusermapboxaccesstoken
