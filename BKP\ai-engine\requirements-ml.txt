# Requisitos para Machine Learning - Engine de IA do sistema TURFEIRAS V3
# Versão focada em aprendizado de máquina e processamento geoespacial

# Bibliotecas básicas
numpy==1.24.3
pandas==2.0.1
scipy==1.10.1
matplotlib==3.7.1
seaborn==0.12.2

# Processamento geoespacial
geopandas==0.13.0
rasterio==1.3.6
shapely==2.0.1
pyproj==3.5.0
folium==0.14.0
geopy==2.3.0

# Machine Learning
scikit-learn==1.2.2
xgboost==1.7.5
lightgbm==3.3.5

# Processamento de imagens
opencv-python==********
Pillow==9.5.0
scikit-image==0.20.0

# APIs e integração
requests==2.29.0
flask==2.3.2
flask-cors==3.0.10

# Armazenamento e serialização
h5py==3.8.0
PyYAML==6.0
joblib==1.2.0
pickle5==0.0.12

# Visualização
plotly==5.14.1

# Processamento de dados
openpyxl==3.1.2
xlrd==2.0.1

# Logging e monitoramento
loguru==0.7.0
tqdm==4.65.0

# Análise estatística
statsmodels==0.14.0
