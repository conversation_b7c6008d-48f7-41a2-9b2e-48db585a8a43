// SPDX-License-Identifier: MIT
// Copyright contributors to the kepler.gl project

import React, {Component} from 'react';
import Base, {BaseProps} from './base';

export default class Delete extends Component<Partial<BaseProps>> {
  static defaultProps = {
    height: '24px',
    viewBox: '0 0 24 24',
    predefinedClassName: 'data-ex-icons-delete'
  };

  render() {
    return (
      <Base
        {...this.props}
        style={{fill: 'none', stroke: 'currentcolor'}}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M18 6 6 18" />
        <path d="m6 6 12 12" />
      </Base>
    );
  }
}
