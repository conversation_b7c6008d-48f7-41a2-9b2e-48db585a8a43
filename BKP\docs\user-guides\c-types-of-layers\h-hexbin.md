# Hexbin

![Hexbin layer](https://d1a3f4spazzrp4.cloudfront.net/kepler.gl/documentation/layers-hexbin.png "Hexbin layer")

Hexbin layers are similar to grid layers. They display distributions of aggregate metrics such as point count within each hexbin, average/max/min/median/sum of a numerical field, or mode/unique count of a string field. Both the color and height dimensions can encode data. Users can adjust the hexagon radius and the space between hexbins.


[Back to table of contents](../README.md)
