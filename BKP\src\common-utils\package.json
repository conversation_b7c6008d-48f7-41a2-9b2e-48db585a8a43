{"name": "@kepler.gl/common-utils", "author": "<PERSON> <<EMAIL>>", "version": "3.1.8", "description": "kepler.gl common utils", "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "keywords": ["babel", "es6", "react", "webgl", "visualization", "deck.gl"], "repository": {"type": "git", "url": "https://github.com/keplergl/kepler.gl.git"}, "scripts": {"build": "rm -fr dist && babel src --out-dir dist --source-maps inline --extensions '.ts,.tsx,.js,.jsx' --ignore '**/*.d.ts'", "build:umd": "NODE_OPTIONS=--openssl-legacy-provider webpack --config ./webpack/umd.js --progress --env.prod", "build:types": "tsc --project ./tsconfig.production.json", "prepublishOnly": "babel-node ../../scripts/license-header/bin --license ../../FILE-HEADER && yarn build && yarn build:types", "stab": "mkdir -p dist && touch dist/index.js"}, "files": ["dist", "umd"], "dependencies": {"@kepler.gl/constants": "3.1.8", "@kepler.gl/types": "3.1.8", "d3-array": "^2.8.0", "global": "^4.3.0", "h3-js": "^3.1.0", "type-analyzer": "0.4.0"}, "nyc": {"sourceMap": false, "instrument": false}, "maintainers": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "engines": {"node": ">=18"}, "volta": {"node": "18.18.2", "yarn": "4.4.0"}, "packageManager": "yarn@4.4.0"}