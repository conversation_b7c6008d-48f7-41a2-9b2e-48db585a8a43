// SPDX-License-Identifier: MIT
// Copyright contributors to the kepler.gl project

import esbuild from 'esbuild';
import {replace} from 'esbuild-plugin-replace';
import {dotenvRun} from '@dotenv-run/esbuild';

import process from 'node:process';
import fs from 'node:fs';
import {join} from 'node:path';
import KeplerPackage from '../../package.json' with {type: 'json'};

const BASE_NODE_MODULES_DIR = './node_modules';

const LIB_DIR = '../../';
const NODE_MODULES_DIR = join(LIB_DIR, 'node_modules');
const SRC_DIR = join(LIB_DIR, 'src');

const NODE_ENV = JSON.stringify(process.env.NODE_ENV || 'production');

const getThirdPartyLibraryAliases = useKeplerNodeModules => {
  const nodeModulesDir = useKeplerNodeModules ? NODE_MODULES_DIR : BASE_NODE_MODULES_DIR;

  const localSources = useKeplerNodeModules
    ? {
        // Suppress useless warnings from react-date-picker's dep
        'tiny-warning': `${SRC_DIR}/utils/src/noop.ts`
      }
    : {};

  return {
    ...localSources,
    react: `${nodeModulesDir}/react`,
    'react-dom': `${nodeModulesDir}/react-dom`,
    'react-redux': `${nodeModulesDir}/react-redux/lib`,
    'styled-components': `${nodeModulesDir}/styled-components`,
    'react-intl': `${nodeModulesDir}/react-intl`,
    'react-palm': `${nodeModulesDir}/react-palm`,
    // kepler.gl and loaders.gl need to use same apache-arrow
    'apache-arrow': `${nodeModulesDir}/apache-arrow`
  };
};

const config = {
  platform: 'browser',
  format: 'iife',
  logLevel: 'info',
  loader: {
    '.js': 'jsx',
    '.css': 'css',
    '.ttf': 'file',
    '.woff': 'file',
    '.woff2': 'file'
  },
  entryPoints: ['src/main.js'],
  outfile: 'dist/bundle.js',
  bundle: true,
  define: {
    NODE_ENV,
    'process.env.NODE_ENV': '"production"'
  },
  // Mark OpenAssistant dependencies as external to skip them during build
  external: [
    '@openassistant/core',
    '@openassistant/ui',
    '@openassistant/echarts',
    '@openassistant/tables',
    '@openassistant/geoda',
    '@openassistant/duckdb',
    '@openassistant/plots',
    '@openassistant/osm',
    '@openassistant/utils',
    '@openassistant/echarts/dist/index.css',
    '@openassistant/ui/dist/index.css',
    // Also mark other problematic deps as external
    'react-resizable-panels',
    'color-interpolate',
    '@monaco-editor/react',
    '@radix-ui/react-collapsible',
    // DuckDB WASM dependencies
    '@duckdb/duckdb-wasm',
    // HTTP client (not needed in frontend build)
    'axios'
  ],
  plugins: [
    dotenvRun({
      verbose: true,
      environment: NODE_ENV,
      root: '../../.env',
      // Only process .env file, not system environment variables
      expand: false,
      override: false
    }),
    // automatically injected kepler.gl package version into the bundle
    replace({
      __PACKAGE_VERSION__: KeplerPackage.version,
      include: /constants\/src\/default-settings\.ts/
    })
  ]
};

// Build for Docker
await esbuild
  .build({
    ...config,
    minify: true,
    sourcemap: false,
    // Add alias resolution for build
    alias: {
      ...getThirdPartyLibraryAliases(true)
    },
    drop: ['console', 'debugger'],
    treeShaking: true,
    metafile: true,
    // Optionally generate a bundle analysis
    plugins: [
      ...config.plugins,
      {
        name: 'bundle-analyzer',
        setup(build) {
          build.onEnd(result => {
            if (result.metafile) {
              // Write bundle analysis to disk
              fs.writeFileSync('meta.json', JSON.stringify(result.metafile));
            }
          });
        }
      }
    ]
  })
  .catch(e => {
    console.error(e);
    process.exit(1);
  })
  .then(() => {
    console.log('✅ Build completed successfully for Docker');
  });
