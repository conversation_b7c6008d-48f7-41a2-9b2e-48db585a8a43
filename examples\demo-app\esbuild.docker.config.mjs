// SPDX-License-Identifier: MIT
// Copyright contributors to the kepler.gl project

import esbuild from 'esbuild';
import {replace} from 'esbuild-plugin-replace';

import process from 'node:process';
import fs from 'node:fs';
import {join} from 'node:path';
import KeplerPackage from '../../package.json' with {type: 'json'};

const BASE_NODE_MODULES_DIR = './node_modules';

const LIB_DIR = '../../';
const NODE_MODULES_DIR = join(LIB_DIR, 'node_modules');
const SRC_DIR = join(LIB_DIR, 'src');

const NODE_ENV = JSON.stringify(process.env.NODE_ENV || 'production');

const getThirdPartyLibraryAliases = useKeplerNodeModules => {
  const nodeModulesDir = useKeplerNodeModules ? NODE_MODULES_DIR : BASE_NODE_MODULES_DIR;

  const localSources = useKeplerNodeModules
    ? {
        // Suppress useless warnings from react-date-picker's dep
        'tiny-warning': `${SRC_DIR}/utils/src/noop.ts`
      }
    : {};

  return {
    ...localSources,
    react: `${nodeModulesDir}/react`,
    'react-dom': `${nodeModulesDir}/react-dom`,
    'react-redux': `${nodeModulesDir}/react-redux/lib`,
    'styled-components': `${nodeModulesDir}/styled-components`,
    'react-intl': `${nodeModulesDir}/react-intl`,
    'react-palm': `${nodeModulesDir}/react-palm`,
    // kepler.gl and loaders.gl need to use same apache-arrow
    'apache-arrow': `${nodeModulesDir}/apache-arrow`
  };
};

const config = {
  platform: 'browser',
  format: 'iife',
  logLevel: 'info',
  loader: {
    '.js': 'jsx',
    '.jsx': 'jsx',
    '.ts': 'tsx',
    '.tsx': 'tsx',
    '.css': 'css',
    '.ttf': 'file',
    '.woff': 'file',
    '.woff2': 'file'
  },
  entryPoints: ['src/main.js'],
  outfile: 'dist/bundle.js',
  bundle: true,
  define: {
    NODE_ENV,
    'process.env.NODE_ENV': '"production"',
    'process.env': '{"NODE_ENV": "production"}',
    'process': '{"env": {"NODE_ENV": "production"}, "browser": true}',
    'global': 'globalThis',
    '__dirname': '""',
    '__filename': '""'
  },
  // No external dependencies - we'll mock everything
  external: [],
  plugins: [
    // automatically injected kepler.gl package version into the bundle
    replace({
      __PACKAGE_VERSION__: KeplerPackage.version,
      include: /constants\/src\/default-settings\.ts/
    }),
    // Mock @openassistant dependencies
    {
      name: 'mock-openassistant',
      setup(build) {
        // Define mock content for each module
        const mockContents = {
          '@duckdb/duckdb-wasm': `
            export const AsyncDuckDB = class { 
              static async create() { return new AsyncDuckDB(); }
              async query() { return []; }
              close() {}
            };
            export const DuckDBDataProtocol = {};
            export default {};
          `,
          'axios': `
            export default {
              get: () => Promise.resolve({ data: {} }),
              post: () => Promise.resolve({ data: {} }),
              put: () => Promise.resolve({ data: {} }),
              delete: () => Promise.resolve({ data: {} })
            };
          `,
          '@openassistant/core': `
            export const useAssistant = () => ({});
            export const GetAssistantModelByProvider = () => ({});
            export const MessageModel = {};
            export default {};
          `,
          '@openassistant/ui': `
            export const AiAssistant = () => null;
            export const ScreenshotWrapper = ({ children }) => children;
            export default {};
          `,
          '@openassistant/plots': `
            export const boxplot = () => ({});
            export const bubbleChart = () => ({});
            export const histogram = () => ({});
            export const pcp = () => ({});
            export const scatterplot = () => ({});
            export default {};
          `,
          '@openassistant/echarts': `
            export const BoxplotComponent = () => null;
            export const BubbleChartComponent = () => null;
            export const HistogramPlotComponent = () => null;
            export const ParallelCoordinateComponent = () => null;
            export const ScatterplotComponent = () => null;
            export default {};
          `,
          '@openassistant/utils': `
            export const extendedTool = () => ({});
            export const generateId = () => 'mock-id';
            export const ToolCache = {};
            export default {};
          `,
          '@openassistant/geoda': `
            export const dataClassify = () => ({});
            export const spatialWeights = () => ({});
            export const globalMoran = () => ({});
            export const spatialRegression = () => ({});
            export const lisa = () => ({});
            export const spatialJoin = () => ({});
            export const spatialFilter = () => ({});
            export const buffer = () => ({});
            export const centroid = () => ({});
            export const dissolve = () => ({});
            export const length = () => ({});
            export const area = () => ({});
            export const perimeter = () => ({});
            export const grid = () => ({});
            export const standardizeVariable = () => ({});
            export const thiessenPolygons = () => ({});
            export const minimumSpanningTree = () => ({});
            export const cartogram = () => ({});
            export const rate = () => ({});
            export default {};
          `,
          '@openassistant/osm': `
            export const getUsStateGeojson = () => ({});
            export const getUsCountyGeojson = () => ({});
            export const getUsZipcodeGeojson = () => ({});
            export const queryUSZipcodes = () => ({});
            export const geocoding = () => ({});
            export const routing = () => ({});
            export const isochrone = () => ({});
            export const roads = () => ({});
            export default {};
          `,
          '@openassistant/tables': `
            export const SpatialJoinComponent = () => null;
            export const DataTableComponent = () => null;
            export const QueryDuckDBComponent = () => null;
            export const QueryDuckDBOutputData = {};
            export default {};
          `,
          '@openassistant/duckdb': `
            export const getDuckDB = () => ({});
            export const LocalQueryAdditionalData = {};
            export const convertArrowRowToObject = () => ({});
            export const localQuery = () => ({});
            export const mergeTables = () => ({});
            export default {};
          `,
          'react-resizable-panels': `
            export const Panel = () => null;
            export const PanelGroup = () => null;
            export const PanelResizeHandle = () => null;
            export default {};
          `,
          'color-interpolate': `
            export default () => '#000000';
          `,
          '@monaco-editor/react': `
            export const Editor = () => null;
            export default () => null;
          `,
          '@radix-ui/react-collapsible': `
            export const Collapsible = () => null;
            export const CollapsibleTrigger = () => null;
            export const CollapsibleContent = () => null;
            export default {};
          `
        };

        // Register resolvers for each module
        Object.keys(mockContents).forEach(module => {
          build.onResolve({ filter: new RegExp(`^${module.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}$`) }, () => ({
            path: module,
            namespace: 'mock-openassistant'
          }));
        });

        // Handle module loading
        build.onLoad({ filter: /.*/, namespace: 'mock-openassistant' }, (args) => ({
          contents: mockContents[args.path] || 'export default {};',
          loader: 'js'
        }));

        // Also handle CSS imports
        build.onResolve({ filter: /@openassistant\/.*\.css$/ }, () => ({
          path: 'mock.css',
          namespace: 'mock-css'
        }));

        build.onLoad({ filter: /.*/, namespace: 'mock-css' }, () => ({
          contents: '',
          loader: 'css'
        }));
      }
    }
  ]
};

// Build for Docker
await esbuild
  .build({
    ...config,
    minify: true,
    sourcemap: false,
    // Add alias resolution for build
    alias: {
      ...getThirdPartyLibraryAliases(true)
    },
    drop: ['console', 'debugger'],
    treeShaking: true
  })
  .catch(e => {
    console.error(e);
    process.exit(1);
  })
  .then(() => {
    console.log('✅ Build completed successfully for Docker');
  });
