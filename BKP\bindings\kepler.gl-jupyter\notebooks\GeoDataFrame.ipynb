{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import geopandas"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame(\n", "    {'City': ['Buenos Aires', 'Brasilia', 'Santiago', 'Bogota', 'Caracas'],\n", "     'Country': ['Argentina', 'Brazil', 'Chile', 'Colombia', 'Venezuela'],\n", "     'Latitude': [-34.58, -15.78, -33.45, 4.60, 10.48],\n", "     'Longitude': [-58.66, -47.91, -70.66, -74.08, -66.86]})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["point_gdf = geopandas.GeoDataFrame(df, geometry=geopandas.points_from_xy(df.Longitude, df.Latitude))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["zipcode_gdf = geopandas.read_file('sf_zip_geo.json')\n", "display(zipcode_gdf.head(5))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": false}, "outputs": [], "source": ["import keplergl\n", "w1 = keplergl.KeplerGl(height=500)\n", "w1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["w1.add_data(data=zipcode_gdf, name=\"zipcode\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["w1.add_data(data=point_gdf, name='cities')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["w1.save_to_html(file_name='city_map.html', read_only=True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 2}