# Docker Compose para Sistema Turfeiras V3
# Configuração completa para análise geomorfológica integrada ao Kepler.gl

services:
  # Banco de dados PostgreSQL com PostGIS
  postgres:
    image: postgis/postgis:15-3.3
    container_name: turfeiras-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=turfeiras
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./data/sql:/docker-entrypoint-initdb.d
    networks:
      - turfeiras-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Serviço de cache Redis
  redis:
    image: redis:7-alpine
    container_name: turfeiras-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - turfeiras-network
    restart: unless-stopped

  # Serviço Principal Kepler.gl com módulos Turfeiras integrados
  kepler-turfeiras:
    build:
      context: .
      dockerfile: docker/Dockerfile.client
      args:
        - NODE_ENV=production
        - BUILDKIT_INLINE_CACHE=1
    container_name: turfeiras-kepler-main
    ports:
      - "8080:80"
    environment:
      # Configurações da aplicação
      - NODE_ENV=production
      - REACT_APP_TURFEIRAS_MODULE=true
      - REACT_APP_AI_ENGINE_URL=http://ai-engine:5000
      
      # Configurações específicas do Turfeiras
      - TURFEIRAS_ANALYSIS_ENABLED=true
      - TURFEIRAS_GEOMORPHOLOGY_MODULE=true
      - TURFEIRAS_DATA_PATH=/app/data
      
      # Configurações de banco de dados
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=turfeiras
      - DB_USER=postgres
      - DB_PASSWORD=postgres
      
    volumes:
      # Volume para dados geomorfológicos
      - ./data:/usr/share/nginx/html/data
      - ./uploads:/usr/share/nginx/html/uploads
      - turfeiras_cache:/app/cache
      
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
      
    networks:
      - turfeiras-network
      
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Serviço de IA para análise geomorfológica (opcional)
  ai-engine:
    build:
      context: .
      dockerfile: docker/Dockerfile.ai
      args:
        - BUILDKIT_INLINE_CACHE=1
    container_name: turfeiras-ai-engine
    ports:
      - "5000:5000"
    environment:
      # Configurações da aplicação
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      - FLASK_ENV=production
      - PORT=5000
      - DEBUG=false
      
      # Configurações de banco de dados
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=turfeiras
      - DB_USER=postgres
      - DB_PASSWORD=postgres
      
      # Configurações de ML para Turfeiras
      - ML_MODEL_PATH=/app/models/saved
      - ML_CACHE_DIR=/app/storage/cache
      - ML_TEMP_DIR=/app/temp
      - TURFEIRAS_MODELS_PATH=/app/models/turfeiras
      
    volumes:
      # Persistir modelos treinados
      - ai_models:/app/models/saved
      - ai_storage:/app/storage
      - ai_logs:/app/logs
      - ai_temp:/app/temp
      
      # Volume para upload de arquivos
      - ./uploads:/app/uploads
      - ./data:/app/data
      
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
        
    networks:
      - turfeiras-network
      
    restart: unless-stopped
    
    profiles:
      - ai

# Volumes nomeados para persistência
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  ai_models:
    driver: local
  ai_storage:
    driver: local
  ai_logs:
    driver: local
  ai_temp:
    driver: local
  turfeiras_cache:
    driver: local

# Rede personalizada
networks:
  turfeiras-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
