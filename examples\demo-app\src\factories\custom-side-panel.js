// SPDX-License-Identifier: MIT
// Copyright contributors to the kepler.gl project

import React from 'react';
import {SidePanelFactory} from '@kepler.gl/components';
// Componente simples para teste
const TurfeirasPanel = () => {
  return (
    <div style={{ padding: '20px', color: '#333' }}>
      <h2 style={{ color: '#2d5a3d', marginBottom: '16px' }}>🌿 TurfeirasBrasil</h2>
      <p>Módulo de turfeiras carregado com sucesso!</p>
      <div style={{ background: '#f8f9fa', padding: '16px', borderRadius: '8px', marginTop: '16px' }}>
        <h3>📊 Dados de Turfeiras</h3>
        <ul>
          <li>5 turfeiras mapeadas</li>
          <li>Pantanal Norte (MT)</li>
          <li>Serra do Cipó (MG)</li>
          <li>Chapada dos Veadeiros (GO)</li>
          <li>Amazônia Central (AM)</li>
          <li>Mata Atlântica (SP)</li>
        </ul>
      </div>
      <div style={{ background: '#e8f5e8', padding: '16px', borderRadius: '8px', marginTop: '16px' }}>
        <h3>🤖 IA Integrada</h3>
        <p>Assistente especializado em análise de turfeiras tropicais.</p>
      </div>
    </div>
  );
};

// Ícone customizado para turfeiras
const PeatlandIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
    <path d="M8 2C6.5 2 5.5 3 5.5 4.5C5.5 6 6.5 7 8 7C9.5 7 10.5 6 10.5 4.5C10.5 3 9.5 2 8 2Z"/>
    <path d="M3 8C3 6.5 4 5.5 5.5 5.5C7 5.5 8 6.5 8 8C8 9.5 7 10.5 5.5 10.5C4 10.5 3 9.5 3 8Z"/>
    <path d="M8 8C8 6.5 9 5.5 10.5 5.5C12 5.5 13 6.5 13 8C13 9.5 12 10.5 10.5 10.5C9 10.5 8 9.5 8 8Z"/>
    <path d="M5.5 10.5C4 10.5 3 11.5 3 13C3 14.5 4 15.5 5.5 15.5C7 15.5 8 14.5 8 13C8 11.5 7 10.5 5.5 10.5Z"/>
    <path d="M10.5 10.5C9 10.5 8 11.5 8 13C8 14.5 9 15.5 10.5 15.5C12 15.5 13 14.5 13 13C13 11.5 12 10.5 10.5 10.5Z"/>
  </svg>
);

function CustomSidePanelFactory(...deps) {
  const SidePanel = SidePanelFactory(...deps);
  
  // Adicionar o painel de turfeiras aos painéis padrão
  const panels = [
    ...SidePanel.defaultPanels,
    {
      id: 'turfeiras',
      label: 'TurfeirasBrasil',
      iconComponent: PeatlandIcon
    }
  ];

  const CustomSidePanel = props => {
    // Se o painel ativo for 'turfeiras', renderizar nosso componente customizado
    if (props.activeSidePanel === 'turfeiras') {
      return (
        <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
          <TurfeirasPanel {...props} />
        </div>
      );
    }
    
    // Caso contrário, usar o painel padrão
    return <SidePanel {...props} panels={panels} />;
  };
  
  return CustomSidePanel;
}

CustomSidePanelFactory.deps = SidePanelFactory.deps;

export default CustomSidePanelFactory;
