{"name": "@kepler.gl/ai-assistant", "author": "<PERSON><PERSON><<EMAIL>>", "version": "3.1.8", "description": "kepler.gl AI assistant", "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "keywords": ["babel", "es6", "react", "webgl", "visualization", "deck.gl"], "repository": {"type": "git", "url": "https://github.com/keplergl/kepler.gl.git"}, "scripts": {"build": "rm -fr dist && babel src --out-dir dist --source-maps inline --extensions '.ts,.tsx,.js,.jsx' --ignore '**/*.d.ts'", "build:umd": "NODE_OPTIONS=--openssl-legacy-provider webpack --config ./webpack/umd.js --progress --env.prod", "build:types": "tsc --project ./tsconfig.production.json", "prepublishOnly": "babel-node ../../scripts/license-header/bin --license ../../FILE-HEADER && yarn build && yarn build:types", "stab": "mkdir -p dist && touch dist/index.js"}, "files": ["dist", "umd"], "dependencies": {"@ai-sdk/anthropic": "^1.2.11", "@ai-sdk/deepseek": "^0.2.14", "@ai-sdk/google": "^1.2.18", "@ai-sdk/xai": "^1.2.16", "@kepler.gl/components": "3.1.8", "@kepler.gl/constants": "3.1.8", "@kepler.gl/layers": "3.1.8", "@kepler.gl/table": "3.1.8", "@kepler.gl/types": "3.1.8", "@kepler.gl/utils": "3.1.8", "@openassistant/core": "^0.5.13", "@openassistant/duckdb": "^0.5.13", "@openassistant/echarts": "^0.5.13", "@openassistant/geoda": "^0.5.13", "@openassistant/osm": "^0.5.13", "@openassistant/plots": "^0.5.13", "@openassistant/tables": "^0.5.13", "@openassistant/ui": "^0.5.13", "@openassistant/utils": "^0.5.13", "color-interpolate": "^1.0.5", "global": "^4.3.0", "ollama-ai-provider-v2": "^0.0.5", "react-intl": "^6.3.0", "usehooks-ts": "^3.1.0"}, "nyc": {"sourceMap": false, "instrument": false}, "maintainers": ["<PERSON><PERSON> <<EMAIL>>"], "engines": {"node": ">=18"}, "volta": {"node": "18.18.2", "yarn": "4.4.0"}, "packageManager": "yarn@4.4.0"}