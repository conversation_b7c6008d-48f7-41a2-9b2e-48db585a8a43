{"scripts": {"start": "node esbuild.config.mjs --start", "build": "node esbuild.config.mjs --build", "deploy": "yarn build && netlify deploy -d dist", "deploy:prod": "yarn build && netlify deploy -d dist --prod", "start:local": "NODE_ENV=local node esbuild.config.mjs --start", "start:local-ai": "NODE_ENV=local node esbuild.config.mjs --start --env.ai", "start:local-deck": "NODE_ENV=local node esbuild.config.mjs --start --env.deck", "start:local-deck-src": "NODE_ENV=local node esbuild.config.mjs --start --env.deck_src", "start:local-loaders-src": "NODE_ENV=local node esbuild.config.mjs --start --env.loaders_src", "start:local-hubble-src": "NODE_ENV=local node esbuild.config.mjs --start --env.hubble_src"}, "dependencies": {"@auth0/auth0-spa-js": "^2.1.2", "@carto/toolkit": "0.0.1-rc.18", "@emotion/is-prop-valid": "^1.2.1", "@kepler.gl/actions": "^3.1.0", "@kepler.gl/ai-assistant": "^3.1.0", "@kepler.gl/cloud-providers": "^3.1.0", "@kepler.gl/components": "^3.1.0", "@kepler.gl/constants": "^3.1.0", "@kepler.gl/duckdb": "^3.1.0", "@kepler.gl/processors": "^3.1.0", "@kepler.gl/reducers": "^3.1.0", "@kepler.gl/schemas": "^3.1.0", "@kepler.gl/styles": "^3.1.0", "@kepler.gl/utils": "^3.1.0", "@loaders.gl/arrow": "^4.3.2", "@loaders.gl/core": "^4.3.2", "@loaders.gl/csv": "^4.3.2", "@loaders.gl/json": "^4.3.2", "@loaders.gl/parquet": "^4.3.2", "@openassistant/core": "^0.5.13", "@openassistant/ui": "^0.5.13", "@types/classnames": "^2.3.1", "@types/keymirror": "^0.1.1", "apache-arrow": ">=15.0.0", "classnames": "^2.2.1", "d3-format": "^2.0.0", "dropbox": "^4.0.12", "esbuild": "^0.25.0", "global": "^4.3.0", "keymirror": "^0.1.1", "markdown-to-jsx": "^7.7.6", "prop-types": "^15.6.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-intl": "^6.3.0", "react-redux": "^8.0.5", "react-resizable-panels": "^2.1.7", "react-router": "3.2.5", "react-router-redux": "^4.0.8", "react-virtualized": "^9.21.0", "redux": "^4.2.1", "redux-actions": "^2.2.1", "redux-logger": "^3.0.6", "redux-thunk": "^1.0.0", "styled-components": "6.1.8", "usehooks-ts": "^3.1.0"}, "resolutions": {"@luma.gl/core": "8.5.21", "@luma.gl/webgl": "8.5.21", "@deck.gl/core": "8.9.27", "@deck.gl/extensions": "8.9.27", "react-vis": "1.11.7"}, "engines": {"node": ">=18"}, "volta": {"node": "18.18.2", "yarn": "4.4.0"}, "packageManager": "yarn@4.4.0", "devDependencies": {"@dotenv-run/esbuild": "^1.5.0", "esbuild-plugin-replace": "^1.4.0"}}