<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

### Table of Contents

- [mapStateUpdaters](#mapstateupdaters)
  - [fitBoundsUpdater](#fitboundsupdater)
  - [INITIAL\_MAP\_STATE](#initial_map_state)
    - [Properties](#properties)
  - [receiveMapConfigUpdater](#receivemapconfigupdater)
  - [resetMapConfigUpdater](#resetmapconfigupdater)
  - [togglePerspectiveUpdater](#toggleperspectiveupdater)
  - [toggleSplitMapUpdater](#togglesplitmapupdater)
  - [updateMapUpdater](#updatemapupdater)

## mapStateUpdaters

Updaters for `mapState` reducer. Can be used in your root reducer to directly modify kepler.gl's state.
Read more about [Using updaters][17]

**Examples**

```javascript
import keplerGlReducer, {mapStateUpdaters} from '@kepler.gl/reducers';
// Root Reducer
const reducers = combineReducers({
 keplerGl: keplerGlReducer,
 app: appReducer
});

const composedReducer = (state, action) => {
 switch (action.type) {
   // click button to close side panel
   case 'CLICK_BUTTON':
     return {
       ...state,
       keplerGl: {
         ...state.keplerGl,
         foo: {
            ...state.keplerGl.foo,
            mapState: mapStateUpdaters.fitBoundsUpdater(
              mapState, {payload: [127.34, 31.09, 127.56, 31.59]]}
            )
         }
       }
     };
 }
 return reducers(state, action);
};

export default composedReducer;
```

### fitBoundsUpdater

Fit map viewport to bounds

-   **Action**: [`fitBounds`][18]

**Parameters**

-   `state` **[Object][19]**
-   `action` **[Object][19]**
    -   `action.payload` **[Array][20]&lt;[number][21]>** bounds as `[lngMin, latMin, lngMax, latMax]`

Returns **[Object][19]** nextState

### INITIAL_MAP_STATE

Default initial `mapState`

#### Properties

-   `pitch` **[number][21]** Default: `0`
-   `bearing` **[number][21]** Default: `0`
-   `latitude` **[number][21]** Default: `37.75043`
-   `longitude` **[number][21]** Default: `-122.34679`
-   `zoom` **[number][21]** Default: `9`
-   `dragRotate` **[boolean][22]** Default: `false`
-   `width` **[number][21]** Default: `800`
-   `height` **[number][21]** Default: `800`
-   `isSplit` **[boolean][22]** Default: `false`

### receiveMapConfigUpdater

Update `mapState` to propagate a new config

-   **Action**: [`receiveMapConfig`][23]

**Parameters**

-   `state` **[Object][19]**
-   `action` **[Object][19]**
    -   `action.payload` **[Object][19]** saved map config
    -   `action.payload.config`   (optional, default `{}`)
    -   `action.payload.options`   (optional, default `{}`)
    -   `action.payload.bounds`   (optional, default `null`)

Returns **[Object][19]** nextState

### resetMapConfigUpdater

reset mapState to initial State

-   **Action**: [`resetMapConfig`][24]

**Parameters**

-   `state` **[Object][19]** `mapState`

Returns **[Object][19]** nextState

### togglePerspectiveUpdater

Toggle between 3d and 2d map.

-   **Action**: [`togglePerspective`][25]

**Parameters**

-   `state` **[Object][19]**

Returns **[Object][19]** nextState

### toggleSplitMapUpdater

Toggle between one or split maps

-   **Action**: [`toggleSplitMap`][26]

**Parameters**

-   `state` **[Object][19]**

Returns **[Object][19]** nextState

### updateMapUpdater

Update map viewport

-   **Action**: [`updateMap`][27]

**Parameters**

-   `state` **[Object][19]**
-   `action` **[Object][19]**
    -   `action.payload` **[Object][19]** viewport

Returns **[Object][19]** nextState

[1]: #mapstateupdaters

[2]: #examples

[3]: #fitboundsupdater

[4]: #parameters

[5]: #initial_map_state

[6]: #properties

[7]: #receivemapconfigupdater

[8]: #parameters-1

[9]: #resetmapconfigupdater

[10]: #parameters-2

[11]: #toggleperspectiveupdater

[12]: #parameters-3

[13]: #togglesplitmapupdater

[14]: #parameters-4

[15]: #updatemapupdater

[16]: #parameters-5

[17]: ../advanced-usage/using-updaters.md

[18]: ../actions/actions.md#fitbounds

[19]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[20]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array

[21]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[22]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

[23]: ../actions/actions.md#receivemapconfig

[24]: ../actions/actions.md#resetmapconfig

[25]: ../actions/actions.md#toggleperspective

[26]: ../actions/actions.md#togglesplitmap

[27]: ../actions/actions.md#updatemap
