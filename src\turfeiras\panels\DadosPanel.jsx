// DadosPanel.jsx
// Painel de visualização de dados para turfeiras

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { TurfeirasApiService } from '../services/TurfeirasApiService';
import { TurfeirasData } from '../types/turfeirasTypes';

// Estilos para o painel
const Container = styled.div`
  padding: 16px;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 20px;
`;

const StatCard = styled.div`
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border-left: 4px solid #2d5a3d;
`;

const StatValue = styled.div`
  font-size: 24px;
  font-weight: 600;
  color: #2d5a3d;
  margin-bottom: 4px;
`;

const StatLabel = styled.div`
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
  font-weight: 500;
`;

const TurfeirasList = styled.div`
  max-height: 400px;
  overflow-y: auto;
`;

const TurfeiraItem = styled.div`
  background: white;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
  }
`;

const TurfeiraNome = styled.div`
  font-weight: 600;
  color: #1e3a2e;
  margin-bottom: 4px;
`;

const TurfeiraInfo = styled.div`
  font-size: 12px;
  color: #666;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
`;

const RefreshButton = styled.button`
  background: #2d5a3d;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  cursor: pointer;
  font-weight: 500;
  margin-bottom: 16px;
  transition: background 0.2s;

  &:hover {
    background: #1e3a2e;
  }

  &:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
`;

/**
 * Componente para o painel de dados de turfeiras
 */
function DadosPanel({ data, onRefresh }) {
  const [stats, setStats] = useState(null);

  useEffect(() => {
    if (data) {
      const calculatedStats = TurfeirasApiService.calculateStatistics(data);
      setStats(calculatedStats);
    }
  }, [data]);

  const handleExportData = () => {
    if (!data) return;

    const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(data));
    const downloadAnchorNode = document.createElement('a');
    downloadAnchorNode.setAttribute("href", dataStr);
    downloadAnchorNode.setAttribute("download", "turfeiras_export.geojson");
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();
  };

  return (
    <Container>
      <RefreshButton onClick={onRefresh}>
        🔄 Atualizar Dados
      </RefreshButton>

      {stats && (
        <StatsGrid>
          <StatCard>
            <StatValue>{stats.totalTurfeiras}</StatValue>
            <StatLabel>Turfeiras</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{stats.areaTotal.toFixed(0)}</StatValue>
            <StatLabel>Hectares</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{stats.carbonoTotal.toFixed(0)}</StatValue>
            <StatLabel>Ton Carbono</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{stats.estados}</StatValue>
            <StatLabel>Estados</StatLabel>
          </StatCard>
        </StatsGrid>
      )}

      {data && (
        <div>
          <h3 style={{ marginBottom: '12px', color: '#1e3a2e' }}>Turfeiras Mapeadas</h3>
          <TurfeirasList>
            {data.features.map((feature) => (
              <TurfeiraItem key={feature.properties.id}>
                <TurfeiraNome>{feature.properties.nome}</TurfeiraNome>
                <TurfeiraInfo>
                  <div>📍 {feature.properties.estado}</div>
                  <div>📏 {feature.properties.area_hectares.toFixed(1)} ha</div>
                  <div>🧪 pH {feature.properties.ph}</div>
                  <div>🌿 {feature.properties.carbono_organico.toFixed(1)}% C</div>
                </TurfeiraInfo>
              </TurfeiraItem>
            ))}
          </TurfeirasList>

          <RefreshButton onClick={handleExportData} style={{ marginTop: '16px' }}>
            💾 Exportar GeoJSON
          </RefreshButton>
        </div>
      )}
    </Container>
  );
}

export default DadosPanel;
