{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open('geojson-data.json', 'r') as f:\n", "    geojson = f.read()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import keplergl\n", "map_1 = keplergl.KeplerGl(height=600)\n", "map_1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["map_1.add_data(geojson, \"geojson\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["map_1.save_to_html(file_name=\"geojson_map.html\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 1}