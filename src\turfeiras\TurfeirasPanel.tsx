import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { TurfeirasData, TurfeirasFeature } from './types/turfeirasTypes';
import { TurfeirasApiService } from './services/TurfeirasApiService';
import DadosPanel from './panels/DadosPanel';
import AnalisePanel from './panels/AnalisePanel';
import AiControlPanel from './panels/AiControlPanel';

const PanelContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f8f9fa;
`;

const TabContainer = styled.div`
  display: flex;
  border-bottom: 1px solid #e9ecef;
  background: white;
`;

const Tab = styled.button<{ active: boolean }>`
  flex: 1;
  padding: 12px 16px;
  border: none;
  background: ${props => props.active ? '#2d5a3d' : 'transparent'};
  color: ${props => props.active ? 'white' : '#666'};
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: ${props => props.active ? '#2d5a3d' : '#f8f9fa'};
  }
`;

const ContentContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 16px;
`;

const Header = styled.div`
  padding: 16px;
  background: linear-gradient(135deg, #1e3a2e 0%, #2d5a3d 100%);
  color: white;
  display: flex;
  align-items: center;
  gap: 12px;
`;

const Title = styled.h2`
  margin: 0;
  font-size: 18px;
  font-weight: 600;
`;

const Icon = styled.span`
  font-size: 24px;
`;

interface TurfeirasPanel {
  // Props do Kepler.gl se necessário
}

const TurfeirasPanel: React.FC<TurfeirasPanel> = () => {
  const [activeTab, setActiveTab] = useState<'dados' | 'analise' | 'ai'>('dados');
  const [turfeirasData, setTurfeirasData] = useState<TurfeirasData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadTurfeirasData();
  }, []);

  const loadTurfeirasData = async () => {
    try {
      setLoading(true);
      const data = await TurfeirasApiService.getTurfeirasData();
      setTurfeirasData(data);
      setError(null);
    } catch (err) {
      setError('Erro ao carregar dados das turfeiras');
      console.error('Erro ao carregar turfeiras:', err);
    } finally {
      setLoading(false);
    }
  };

  const renderContent = () => {
    if (loading) {
      return <div>Carregando dados das turfeiras...</div>;
    }

    if (error) {
      return <div>Erro: {error}</div>;
    }

    switch (activeTab) {
      case 'dados':
        return <DadosPanel data={turfeirasData} onRefresh={loadTurfeirasData} />;
      case 'analise':
        return <AnalisePanel data={turfeirasData} />;
      case 'ai':
        return <AiControlPanel data={turfeirasData} />;
      default:
        return null;
    }
  };

  return (
    <PanelContainer>
      <Header>
        <Icon>🌿</Icon>
        <Title>TurfeirasBrasil</Title>
      </Header>

      <TabContainer>
        <Tab
          active={activeTab === 'dados'}
          onClick={() => setActiveTab('dados')}
        >
          📊 Dados
        </Tab>
        <Tab
          active={activeTab === 'analise'}
          onClick={() => setActiveTab('analise')}
        >
          📈 Análise
        </Tab>
        <Tab
          active={activeTab === 'ai'}
          onClick={() => setActiveTab('ai')}
        >
          🤖 IA
        </Tab>
      </TabContainer>

      <ContentContainer>
        {renderContent()}
      </ContentContainer>
    </PanelContainer>
  );
};

export default TurfeirasPanel;