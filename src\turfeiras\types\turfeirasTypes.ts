/**
 * Tipos para o sistema de análise de turfeiras
 * Baseado nos requisitos do projeto de mestrado sobre turfeiras tropicais
 */

// Tipo base para turfeiras
export interface Turfeira {
  id: string;
  nome: string;
  latitude: number;
  longitude: number;
  tipo: TipoTurfeira;
  profundidade: number; // em metros
  carbono: number; // em toneladas por hectare
  area: number; // em km²
  dataColeta?: Date;
  descricao?: string;
}

// Tipos de turfeiras conforme classificação
export type TipoTurfeira = 'Fibrosa' | 'Hêmica' | 'Sáprica';

// Dados geomorfológicos estendidos
export interface DadosGeomorfologicos {
  declividade: number; // em percentual
  altitudeMedia: number; // em metros
  distanciaCorposAgua: number; // em metros
  vegetacaoPredominante: string;
  usoSolo: string;
}

// Interface estendida com dados geomorfológicos
export interface TurfeiraGeomorfologica extends Turfeira {
  geomorfologia: DadosGeomorfologicos;
}

// Dados de impacto antrópico
export interface ImpactoAntropico {
  proximidadeUrbana: number; // 0-10, onde 10 é alta proximidade
  taxaDesmatamento: number; // em percentual por ano
  indiceImpacto: number; // 0-5, onde 5 é alto impacto
  atividadesHumanas: string[]; // agricultura, mineração, etc.
}

// Interface estendida com dados de impacto
export interface TurfeiraComImpacto extends Turfeira {
  impacto: ImpactoAntropico;
}

// Dados de análise temporal
export interface DadosTemporal {
  ndvi: {
    atual: number;
    historico: { data: Date; valor: number }[];
    tendencia: 'aumento' | 'estavel' | 'diminuicao';
  };
  evi: {
    atual: number;
    historico: { data: Date; valor: number }[];
    tendencia: 'aumento' | 'estavel' | 'diminuicao';
  };
  umidade: {
    atual: number;
    historico: { data: Date; valor: number }[];
    tendencia: 'aumento' | 'estavel' | 'diminuicao';
  };
}

// Interface estendida com dados temporais
export interface TurfeiraTemporal extends Turfeira {
  temporal: DadosTemporal;
}

// Parâmetros para classificação usando Random Forest
export interface ParamsRandomForest {
  nEstimators: number;
  maxDepth: number;
  minSamplesSplit: number;
  features: string[];
}

// Estatísticas gerais das turfeiras
export interface EstatisticasTurfeiras {
  totalTurfeiras: number;
  areaTotal: number;
  estoqueCarbonoTotal: number;
  profundidadeMedia: number;
  distribuicaoTipos?: Record<TipoTurfeira, number>;
  indicePreservacao?: number; // 0-100%
  vulnerabilidade?: number; // 0-100%
}

// Parâmetros para imagens de satélite
export interface ParametrosImagem {
  satellite: 'Sentinel-2' | 'Landsat-8';
  startDate: string; // ISO format
  endDate: string; // ISO format
  cloudCoverage: number; // 0-100%
  bandas: string[];
  regiao: {
    tipo: 'bbox' | 'polygon';
    coordenadas: number[][];
  };
}

// Políticas de conservação
export interface PoliticaConservacao {
  id: string;
  titulo: string;
  descricao: string;
  recomendacoes: string[];
  areasAlvo: string[];
  prioridade: 'baixa' | 'media' | 'alta';
  fundamentacao: string;
  fontes: string[];
}

// Tipos GeoJSON para turfeiras
export interface TurfeirasFeature {
  type: 'Feature';
  properties: {
    id: number;
    nome: string;
    estado: string;
    municipio: string;
    area_hectares: number;
    profundidade_media: number;
    ph: number;
    carbono_organico: number;
    tipo_vegetacao: string;
    status_conservacao: string;
    data_coleta: string;
    temperatura_agua: number;
    condutividade: number;
    oxigenio_dissolvido: number;
    especies_dominantes: string;
    ameacas: string;
    importancia_ecologica: string;
  };
  geometry: {
    type: 'Point';
    coordinates: [number, number]; // [longitude, latitude]
  };
}

export interface TurfeirasData {
  type: 'FeatureCollection';
  features: TurfeirasFeature[];
}

// Resposta do serviço de API
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: string;
}
