import React, { useState, useMemo } from 'react';
import styled from 'styled-components';
import { TurfeirasData } from '../types/turfeirasTypes';
import { TurfeirasApiService } from '../services/TurfeirasApiService';

const Container = styled.div`
  padding: 16px;
`;

const FilterSection = styled.div`
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
`;

const FilterGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 12px;
`;

const FilterItem = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

const Label = styled.label`
  font-size: 12px;
  font-weight: 500;
  color: #666;
  text-transform: uppercase;
`;

const Select = styled.select`
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
`;

const Input = styled.input`
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
`;

const AnalysisCard = styled.div`
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
`;

const CardTitle = styled.h3`
  margin: 0 0 12px 0;
  color: #1e3a2e;
  font-size: 16px;
`;

const MetricGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
`;

const MetricItem = styled.div`
  text-align: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
`;

const MetricValue = styled.div`
  font-size: 20px;
  font-weight: 600;
  color: #2d5a3d;
  margin-bottom: 4px;
`;

const MetricLabel = styled.div`
  font-size: 11px;
  color: #666;
  text-transform: uppercase;
`;

const ChartContainer = styled.div`
  height: 200px;
  background: #f8f9fa;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-style: italic;
`;

interface AnalisePanel {
  data: TurfeirasData | null;
}

const AnalisePanel: React.FC<AnalisePanel> = ({ data }) => {
  const [filters, setFilters] = useState({
    estado: '',
    statusConservacao: '',
    areaMinima: '',
    phRange: [3, 6] as [number, number]
  });

  const filteredData = useMemo(() => {
    if (!data) return null;

    return TurfeirasApiService.filterTurfeiras(data, {
      estado: filters.estado || undefined,
      statusConservacao: filters.statusConservacao || undefined,
      areaMinima: filters.areaMinima ? parseFloat(filters.areaMinima) : undefined,
      phRange: filters.phRange
    });
  }, [data, filters]);

  const stats = useMemo(() => {
    if (!filteredData) return null;
    return TurfeirasApiService.calculateStatistics(filteredData);
  }, [filteredData]);

  const estados = useMemo(() => {
    if (!data) return [];
    return [...new Set(data.features.map(f => f.properties.estado))];
  }, [data]);

  const statusOptions = useMemo(() => {
    if (!data) return [];
    return [...new Set(data.features.map(f => f.properties.status_conservacao))];
  }, [data]);

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  if (!data) {
    return (
      <Container>
        <div>Carregue os dados das turfeiras para ver as análises.</div>
      </Container>
    );
  }

  return (
    <Container>
      <FilterSection>
        <CardTitle>🔍 Filtros de Análise</CardTitle>
        <FilterGrid>
          <FilterItem>
            <Label>Estado</Label>
            <Select
              value={filters.estado}
              onChange={(e) => handleFilterChange('estado', e.target.value)}
            >
              <option value="">Todos os estados</option>
              {estados.map(estado => (
                <option key={estado} value={estado}>{estado}</option>
              ))}
            </Select>
          </FilterItem>

          <FilterItem>
            <Label>Status de Conservação</Label>
            <Select
              value={filters.statusConservacao}
              onChange={(e) => handleFilterChange('statusConservacao', e.target.value)}
            >
              <option value="">Todos os status</option>
              {statusOptions.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </Select>
          </FilterItem>

          <FilterItem>
            <Label>Área Mínima (ha)</Label>
            <Input
              type="number"
              value={filters.areaMinima}
              onChange={(e) => handleFilterChange('areaMinima', e.target.value)}
              placeholder="Ex: 1000"
            />
          </FilterItem>

          <FilterItem>
            <Label>pH Mínimo</Label>
            <Input
              type="number"
              step="0.1"
              value={filters.phRange[0]}
              onChange={(e) => handleFilterChange('phRange', [parseFloat(e.target.value) || 3, filters.phRange[1]])}
            />
          </FilterItem>
        </FilterGrid>
      </FilterSection>

      {stats && (
        <>
          <AnalysisCard>
            <CardTitle>📊 Estatísticas Filtradas</CardTitle>
            <MetricGrid>
              <MetricItem>
                <MetricValue>{stats.totalTurfeiras}</MetricValue>
                <MetricLabel>Turfeiras</MetricLabel>
              </MetricItem>
              <MetricItem>
                <MetricValue>{stats.areaTotal.toFixed(0)}</MetricValue>
                <MetricLabel>Hectares</MetricLabel>
              </MetricItem>
              <MetricItem>
                <MetricValue>{stats.carbonoTotal.toFixed(0)}</MetricValue>
                <MetricLabel>Ton Carbono</MetricLabel>
              </MetricItem>
              <MetricItem>
                <MetricValue>{stats.phMedio.toFixed(1)}</MetricValue>
                <MetricLabel>pH Médio</MetricLabel>
              </MetricItem>
              <MetricItem>
                <MetricValue>{stats.profundidadeMedia.toFixed(1)}</MetricValue>
                <MetricLabel>Prof. Média (m)</MetricLabel>
              </MetricItem>
            </MetricGrid>
          </AnalysisCard>

          <AnalysisCard>
            <CardTitle>📈 Distribuição por Estado</CardTitle>
            <ChartContainer>
              Gráfico de distribuição por estado
              <br />
              (Implementação futura com biblioteca de gráficos)
            </ChartContainer>
          </AnalysisCard>

          <AnalysisCard>
            <CardTitle>🌡️ Análise de pH</CardTitle>
            <ChartContainer>
              Histograma de distribuição de pH
              <br />
              (Implementação futura com biblioteca de gráficos)
            </ChartContainer>
          </AnalysisCard>
        </>
      )}
    </Container>
  );
};

export default AnalisePanel;