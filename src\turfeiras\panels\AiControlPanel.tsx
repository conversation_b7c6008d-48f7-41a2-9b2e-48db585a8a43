import React, { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';
import { TurfeirasData } from '../types/turfeirasTypes';
import { TurfeirasApiService } from '../services/TurfeirasApiService';

const Container = styled.div`
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
`;

const ChatContainer = styled.div`
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

const ChatHeader = styled.div`
  background: linear-gradient(135deg, #1e3a2e 0%, #2d5a3d 100%);
  color: white;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
`;

const ChatMessages = styled.div`
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  max-height: 400px;
`;

const Message = styled.div<{ isUser: boolean }>`
  display: flex;
  margin-bottom: 16px;
  align-items: flex-start;
  gap: 12px;
  ${props => props.isUser && 'flex-direction: row-reverse;'}
`;

const Avatar = styled.div<{ isUser: boolean }>`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  background: ${props => props.isUser ? '#e3f2fd' : '#e8f5e8'};
  flex-shrink: 0;
`;

const MessageBubble = styled.div<{ isUser: boolean }>`
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 18px;
  background: ${props => props.isUser ? '#2d5a3d' : '#f8f9fa'};
  color: ${props => props.isUser ? 'white' : '#333'};
  line-height: 1.4;
  word-wrap: break-word;
`;

const InputContainer = styled.div`
  padding: 16px;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 12px;
`;

const Input = styled.input`
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 24px;
  outline: none;
  font-size: 14px;

  &:focus {
    border-color: #2d5a3d;
  }
`;

const SendButton = styled.button`
  background: #2d5a3d;
  color: white;
  border: none;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;

  &:hover {
    background: #1e3a2e;
  }

  &:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
`;

const QuickActions = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
`;

const QuickActionButton = styled.button`
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 16px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: #e9ecef;
    border-color: #2d5a3d;
  }
`;

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

interface AiControlPanel {
  data: TurfeirasData | null;
}

const AiControlPanel: React.FC<AiControlPanel> = ({ data }) => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: 'Olá! Sou seu assistente de IA especializado em análise de turfeiras. Posso ajudar você a analisar dados, calcular estoques de carbono, identificar padrões e sugerir estratégias de conservação. Como posso ajudar?',
      isUser: false,
      timestamp: new Date()
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const quickActions = [
    'Calcular estoque de carbono',
    'Analisar distribuição por estado',
    'Identificar áreas de risco',
    'Sugerir estratégias de conservação',
    'Comparar características físico-químicas',
    'Avaliar biodiversidade'
  ];

  const generateAIResponse = (userMessage: string): string => {
    const lowerMessage = userMessage.toLowerCase();

    if (!data) {
      return 'Para fornecer análises precisas, preciso que os dados das turfeiras sejam carregados primeiro. Por favor, carregue os dados na aba "Dados".';
    }

    const stats = TurfeirasApiService.calculateStatistics(data);

    if (lowerMessage.includes('carbono') || lowerMessage.includes('carbon')) {
      return `Com base nos dados carregados, aqui está a análise do estoque de carbono:

📊 **Estoque Total de Carbono:** ${stats.carbonoTotal.toFixed(0)} toneladas

🏆 **Maiores estoques por turfeira:**
${data.features
  .sort((a, b) => (b.properties.carbono_organico * b.properties.area_hectares) - (a.properties.carbono_organico * a.properties.area_hectares))
  .slice(0, 3)
  .map((f, i) => `${i + 1}. ${f.properties.nome}: ${((f.properties.carbono_organico * f.properties.area_hectares) / 100).toFixed(0)} ton`)
  .join('\n')}

💡 **Recomendação:** As turfeiras com maior estoque de carbono devem ter prioridade máxima de conservação para mitigação das mudanças climáticas.`;
    }

    if (lowerMessage.includes('conservação') || lowerMessage.includes('preserva') || lowerMessage.includes('estratégia')) {
      const emRisco = data.features.filter(f => f.properties.status_conservacao.toLowerCase().includes('risco'));
      return `🛡️ **Estratégias de Conservação Recomendadas:**

**Situação Atual:**
- ${stats.totalTurfeiras} turfeiras mapeadas
- ${emRisco.length} em situação de risco

**Prioridades Imediatas:**
1. **Monitoramento contínuo** das turfeiras em risco
2. **Controle de acesso** em áreas sensíveis
3. **Restauração ecológica** de áreas degradadas
4. **Educação ambiental** para comunidades locais

**Ações Específicas:**
${emRisco.length > 0 ?
  `- Atenção especial para: ${emRisco.map(f => f.properties.nome).join(', ')}` :
  '- Manter programas de monitoramento preventivo'}
- Implementar sensores de pH e nível d'água
- Criar corredores ecológicos entre turfeiras`;
    }

    if (lowerMessage.includes('estado') || lowerMessage.includes('distribuição') || lowerMessage.includes('região')) {
      const estadosCount = data.features.reduce((acc, f) => {
        acc[f.properties.estado] = (acc[f.properties.estado] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      return `🗺️ **Distribuição por Estado:**

${Object.entries(estadosCount)
  .sort(([,a], [,b]) => b - a)
  .map(([estado, count]) => `📍 **${estado}:** ${count} turfeira(s)`)
  .join('\n')}

**Análise Regional:**
- **Maior concentração:** ${Object.entries(estadosCount).sort(([,a], [,b]) => b - a)[0][0]}
- **Diversidade geográfica:** ${Object.keys(estadosCount).length} estados representados
- **Recomendação:** Expandir mapeamento em regiões sub-representadas`;
    }

    if (lowerMessage.includes('ph') || lowerMessage.includes('acidez') || lowerMessage.includes('químic')) {
      const phValues = data.features.map(f => f.properties.ph);
      const minPh = Math.min(...phValues);
      const maxPh = Math.max(...phValues);

      return `🧪 **Análise Físico-Química:**

**pH das Turfeiras:**
- **Média:** ${stats.phMedio.toFixed(1)}
- **Variação:** ${minPh} - ${maxPh}
- **Mais ácida:** ${data.features.find(f => f.properties.ph === minPh)?.properties.nome} (pH ${minPh})
- **Menos ácida:** ${data.features.find(f => f.properties.ph === maxPh)?.properties.nome} (pH ${maxPh})

**Interpretação:**
- pH baixo indica boa preservação da matéria orgânica
- Condições anaeróbicas adequadas para acúmulo de carbono
- Valores típicos para turfeiras tropicais brasileiras

**Monitoramento:** Variações no pH podem indicar alterações no regime hídrico ou impactos antrópicos.`;
    }

    if (lowerMessage.includes('área') || lowerMessage.includes('tamanho') || lowerMessage.includes('hectare')) {
      const areas = data.features.map(f => f.properties.area_hectares);
      const maiorArea = Math.max(...areas);
      const menorArea = Math.min(...areas);

      return `📏 **Análise de Áreas:**

**Estatísticas:**
- **Área total mapeada:** ${stats.areaTotal.toFixed(0)} hectares
- **Área média:** ${(stats.areaTotal / stats.totalTurfeiras).toFixed(0)} ha/turfeira
- **Maior turfeira:** ${data.features.find(f => f.properties.area_hectares === maiorArea)?.properties.nome} (${maiorArea} ha)
- **Menor turfeira:** ${data.features.find(f => f.properties.area_hectares === menorArea)?.properties.nome} (${menorArea} ha)

**Recomendações:**
- Priorizar conservação das maiores áreas (maior estoque de carbono)
- Conectar turfeiras menores através de corredores ecológicos
- Expandir mapeamento para identificar novas áreas`;
    }

    if (lowerMessage.includes('biodiversidade') || lowerMessage.includes('espécie') || lowerMessage.includes('vegetação')) {
      const vegetacaoTipos = [...new Set(data.features.map(f => f.properties.tipo_vegetacao))];

      return `🌿 **Análise de Biodiversidade:**

**Tipos de Vegetação Identificados:**
${vegetacaoTipos.map(tipo => `• ${tipo}`).join('\n')}

**Espécies Dominantes por Turfeira:**
${data.features.map(f => `🌱 **${f.properties.nome}:** ${f.properties.especies_dominantes}`).join('\n\n')}

**Importância Ecológica:**
${data.features.map(f => `🔬 **${f.properties.nome}:** ${f.properties.importancia_ecologica}`).join('\n\n')}

**Conservação da Biodiversidade:**
- Manter diversidade de habitats
- Proteger espécies endêmicas e raras
- Monitorar mudanças na composição de espécies`;
    }

    // Resposta padrão
    return `Interessante pergunta! Com base nos dados das ${stats.totalTurfeiras} turfeiras mapeadas, posso analisar diversos aspectos:

🔍 **Análises Disponíveis:**
• Estoques de carbono (${stats.carbonoTotal.toFixed(0)} ton total)
• Distribuição geográfica (${stats.estados} estados)
• Características físico-químicas (pH médio: ${stats.phMedio.toFixed(1)})
• Status de conservação e ameaças
• Biodiversidade e espécies dominantes

💡 **Sugestão:** Seja mais específico sobre qual aspecto gostaria de explorar. Por exemplo:
- "Analise o estoque de carbono"
- "Quais turfeiras estão em risco?"
- "Compare as características por estado"`;
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputValue,
      isUser: true,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    // Simula delay da IA
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        text: generateAIResponse(inputValue),
        isUser: false,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, aiResponse]);
      setIsLoading(false);
    }, 1000);
  };

  const handleQuickAction = (action: string) => {
    setInputValue(action);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  return (
    <Container>
      <QuickActions>
        {quickActions.map((action, index) => (
          <QuickActionButton
            key={index}
            onClick={() => handleQuickAction(action)}
          >
            {action}
          </QuickActionButton>
        ))}
      </QuickActions>

      <ChatContainer>
        <ChatHeader>
          <span style={{ fontSize: '20px' }}>🤖</span>
          <div>
            <div style={{ fontWeight: 600 }}>Assistente IA - Turfeiras</div>
            <div style={{ fontSize: '12px', opacity: 0.8 }}>
              Especialista em análise de turfeiras tropicais
            </div>
          </div>
        </ChatHeader>

        <ChatMessages>
          {messages.map((message) => (
            <Message key={message.id} isUser={message.isUser}>
              <Avatar isUser={message.isUser}>
                {message.isUser ? '👤' : '🤖'}
              </Avatar>
              <MessageBubble isUser={message.isUser}>
                {message.text.split('\n').map((line, i) => (
                  <div key={i}>{line}</div>
                ))}
              </MessageBubble>
            </Message>
          ))}
          {isLoading && (
            <Message isUser={false}>
              <Avatar isUser={false}>🤖</Avatar>
              <MessageBubble isUser={false}>
                Analisando dados... 🔄
              </MessageBubble>
            </Message>
          )}
          <div ref={messagesEndRef} />
        </ChatMessages>

        <InputContainer>
          <Input
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Digite sua pergunta sobre as turfeiras..."
            disabled={isLoading}
          />
          <SendButton onClick={handleSendMessage} disabled={isLoading || !inputValue.trim()}>
            ➤
          </SendButton>
        </InputContainer>
      </ChatContainer>
    </Container>
  );
};

export default AiControlPanel;