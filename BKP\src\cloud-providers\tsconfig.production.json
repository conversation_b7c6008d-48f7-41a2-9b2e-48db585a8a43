{"compilerOptions": {"target": "es2020", "allowJs": false, "checkJs": false, "jsx": "react", "module": "esnext", "moduleResolution": "node", "declaration": true, "emitDeclarationOnly": true, "noImplicitAny": false, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "outDir": "dist", "sourceMap": true, "strictNullChecks": true, "suppressImplicitAnyIndexErrors": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "strict": true, "resolveJsonModule": true, "isolatedModules": true, "baseUrl": "./src"}, "include": ["src"]}