// SPDX-License-Identifier: MIT
// Copyright contributors to the kepler.gl project

import React, {Component} from 'react';
import PropTypes from 'prop-types';
import Base from '../base';

export default class Noise extends Component {
  static propTypes = {
    /** Set the height of the icon, ex. '16px' */
    height: PropTypes.string
  };

  static defaultProps = {
    height: '16px',
    viewBox: '0 0 16 16',
    predefinedClassName: 'data-ex-icons-noise'
  };

  render() {
    return (
      <Base {...this.props}>
        <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
          <path d="M11.1778 5.42465C11.3485 5.24574 11.3403 4.96381 11.1594 4.79496C10.9786 4.6261 10.6937 4.63425 10.523 4.81316C10.3524 4.99208 10.3606 5.274 10.5414 5.44286C10.7222 5.61172 11.0072 5.60356 11.1778 5.42465Z" />
          <path d="M10.3358 5.65847C10.5166 5.82733 10.5248 6.10925 10.3542 6.28817C10.1835 6.46708 9.89856 6.47523 9.71775 6.30637C9.53693 6.13752 9.5287 5.85559 9.69936 5.67668C9.87003 5.49777 10.155 5.48961 10.3358 5.65847Z" />
          <path d="M9.53016 7.15179C9.70082 6.97287 9.69259 6.69095 9.51177 6.52209C9.33096 6.35323 9.04603 6.36139 8.87537 6.5403C8.7047 6.71921 8.71293 7.00114 8.89375 7.16999C9.07456 7.33885 9.35949 7.3307 9.53016 7.15179Z" />
          <path d="M8.70637 8.01541C8.87703 7.83649 8.8688 7.55457 8.68799 7.38571C8.50717 7.21685 8.22224 7.22501 8.05158 7.40392C7.88092 7.58283 7.88915 7.86476 8.06996 8.03361C8.25078 8.20247 8.53571 8.19432 8.70637 8.01541Z" />
          <path d="M10.4519 9.64561C10.6225 9.4667 10.6143 9.18477 10.4335 9.01591C10.2527 8.84706 9.96775 8.85521 9.79709 9.03412C9.62643 9.21303 9.63466 9.49496 9.81547 9.66382C9.99629 9.83267 10.2812 9.82452 10.4519 9.64561Z" />
          <path d="M9.57934 8.83043C9.75 8.65151 9.74177 8.36959 9.56096 8.20073C9.38014 8.03188 9.09521 8.04003 8.92455 8.21894C8.75389 8.39785 8.76212 8.67978 8.94293 8.84863C9.12375 9.01749 9.40868 9.00934 9.57934 8.83043Z" />
          <path d="M12.1976 11.276C12.3683 11.0971 12.36 10.8152 12.1792 10.6463C11.9984 10.4775 11.7135 10.4856 11.5428 10.6645C11.3722 10.8435 11.3804 11.1254 11.5612 11.2942C11.742 11.4631 12.0269 11.4549 12.1976 11.276Z" />
          <path d="M11.3251 10.4608C11.4957 10.2819 11.4875 10 11.3067 9.83115C11.1259 9.66229 10.8409 9.67044 10.6703 9.84936C10.4996 10.0283 10.5078 10.3102 10.6887 10.479C10.8695 10.6479 11.1544 10.6398 11.3251 10.4608Z" />
          <path d="M5.41102 11.4699C5.58168 11.291 5.57345 11.0091 5.39263 10.8402C5.21182 10.6713 4.92689 10.6795 4.75623 10.8584C4.58556 11.0373 4.59379 11.3192 4.77461 11.4881C4.95542 11.657 5.24035 11.6488 5.41102 11.4699Z" />
          <path d="M6.28388 12.285C6.45454 12.106 6.44631 11.8241 6.26549 11.6553C6.08468 11.4864 5.79975 11.4946 5.62909 11.6735C5.45843 11.8524 5.46666 12.1343 5.64747 12.3032C5.82829 12.472 6.11322 12.4639 6.28388 12.285Z" />
          <path d="M11.2574 8.15229C11.4382 8.32115 11.4464 8.60308 11.2758 8.78199C11.1051 8.9609 10.8202 8.96905 10.6394 8.8002C10.4586 8.63134 10.4503 8.34941 10.621 8.1705C10.7916 7.99159 11.0766 7.98344 11.2574 8.15229Z" />
          <path d="M7.98063 12.2367C8.15129 12.0578 8.14306 11.7758 7.96225 11.607C7.78143 11.4381 7.4965 11.4463 7.32584 11.6252C7.15518 11.8041 7.16341 12.086 7.34423 12.2549C7.52504 12.4237 7.80997 12.4156 7.98063 12.2367Z" />
          <path d="M10.4031 7.96697C10.5738 7.78805 10.5656 7.50613 10.3847 7.33727C10.2039 7.16842 9.919 7.17657 9.74833 7.35548C9.57767 7.53439 9.5859 7.81632 9.76672 7.98517C9.94753 8.15403 10.2325 8.14588 10.4031 7.96697Z" />
          <path d="M12.1486 9.59717C12.3193 9.41826 12.3111 9.13633 12.1303 8.96748C11.9494 8.79862 11.6645 8.80677 11.4938 8.98568C11.3232 9.1646 11.3314 9.44652 11.5122 9.61538C11.693 9.78423 11.978 9.77608 12.1486 9.59717Z" />
          <path d="M12.0997 7.91837C12.2703 7.73945 12.2621 7.45753 12.0813 7.28867C11.9005 7.11982 11.6155 7.12797 11.4449 7.30688C11.2742 7.48579 11.2824 7.76772 11.4633 7.93657C11.6441 8.10543 11.929 8.09728 12.0997 7.91837Z" />
          <path d="M6.21663 9.97657C6.39745 10.1454 6.40568 10.4274 6.23501 10.6063C6.06435 10.7852 5.77942 10.7933 5.59861 10.6245C5.41779 10.4556 5.40956 10.1737 5.58022 9.99478C5.75089 9.81587 6.03582 9.80772 6.21663 9.97657Z" />
          <path d="M7.08928 10.7916C7.2701 10.9605 7.27833 11.2424 7.10766 11.4213C6.937 11.6002 6.65207 11.6084 6.47126 11.4395C6.29044 11.2706 6.28221 10.9887 6.45287 10.8098C6.62354 10.6309 6.90847 10.6227 7.08928 10.7916Z" />
          <path d="M8.78625 10.7432C8.96706 10.9121 8.97529 11.194 8.80463 11.3729C8.63397 11.5518 8.34904 11.56 8.16822 11.3911C7.98741 11.2223 7.97918 10.9403 8.14984 10.7614C8.3205 10.5825 8.60543 10.5744 8.78625 10.7432Z" />
          <path d="M8.835 12.422C9.01582 12.5909 9.02405 12.8728 8.85339 13.0517C8.68272 13.2306 8.3978 13.2388 8.21698 13.0699C8.03617 12.9011 8.02794 12.6191 8.1986 12.4402C8.36926 12.2613 8.65419 12.2532 8.835 12.422Z" />
          <path d="M12.9052 6.42489C13.086 6.59375 13.0942 6.87568 12.9236 7.05459C12.7529 7.2335 12.468 7.24165 12.2872 7.0728C12.1063 6.90394 12.0981 6.62201 12.2688 6.4431C12.4394 6.26419 12.7244 6.25604 12.9052 6.42489Z" />
          <path d="M11.2085 6.47349C11.3893 6.64235 11.3976 6.92427 11.2269 7.10319C11.0562 7.2821 10.7713 7.29025 10.5905 7.12139C10.4097 6.95254 10.4015 6.67061 10.5721 6.4917C10.7428 6.31279 11.0277 6.30464 11.2085 6.47349Z" />
          <path d="M12.9541 8.10401C13.135 8.27287 13.1432 8.5548 12.9725 8.73371C12.8019 8.91262 12.5169 8.92077 12.3361 8.75192C12.1553 8.58306 12.1471 8.30113 12.3177 8.12222C12.4884 7.94331 12.7733 7.93516 12.9541 8.10401Z" />
          <path d="M9.60982 9.87969C9.79063 10.0486 9.79886 10.3305 9.6282 10.5094C9.45754 10.6883 9.17261 10.6965 8.9918 10.5276C8.81098 10.3587 8.80275 10.0768 8.97341 9.8979C9.14408 9.71899 9.429 9.71084 9.60982 9.87969Z" />
          <path d="M7.04052 9.11295C7.22134 9.28181 7.22957 9.56373 7.05891 9.74265C6.88824 9.92156 6.60331 9.92971 6.4225 9.76085C6.24168 9.592 6.23345 9.31007 6.40412 9.13116C6.57478 8.95225 6.85971 8.94409 7.04052 9.11295Z" />
          <path d="M7.93145 10.5577C8.10211 10.3788 8.09388 10.0968 7.91307 9.92797C7.73225 9.75912 7.44732 9.76727 7.27666 9.94618C7.106 10.1251 7.11423 10.407 7.29504 10.5759C7.47586 10.7447 7.76079 10.7366 7.93145 10.5577Z" />
          <path d="M9.65879 11.5582C9.8396 11.7271 9.84784 12.009 9.67717 12.1879C9.50651 12.3668 9.22158 12.375 9.04077 12.2061C8.85995 12.0373 8.85172 11.7554 9.02238 11.5764C9.19305 11.3975 9.47798 11.3894 9.65879 11.5582Z" />
          <path d="M12.0504 6.23973C12.221 6.06081 12.2128 5.77889 12.032 5.61003C11.8512 5.44118 11.5662 5.44933 11.3956 5.62824C11.2249 5.80715 11.2332 6.08908 11.414 6.25793C11.5948 6.42679 11.8797 6.41864 12.0504 6.23973Z" />
          <path d="M7.86399 8.24933C8.0448 8.41819 8.05303 8.70011 7.88237 8.87903C7.71171 9.05794 7.42678 9.06609 7.24597 8.89723C7.06515 8.72838 7.05692 8.44645 7.22758 8.26754C7.39825 8.08863 7.68317 8.08047 7.86399 8.24933Z" />
          <path d="M8.73706 9.06435C8.91788 9.23321 8.92611 9.51513 8.75545 9.69405C8.58478 9.87296 8.29986 9.88111 8.11904 9.71226C7.93823 9.5434 7.93 9.26147 8.10066 9.08256C8.27132 8.90365 8.55625 8.8955 8.73706 9.06435Z" />
          <path d="M10.4825 10.6946C10.6633 10.8634 10.6715 11.1453 10.5009 11.3243C10.3302 11.5032 10.0453 11.5113 9.86445 11.3425C9.68363 11.1736 9.6754 10.8917 9.84606 10.7128C10.0167 10.5339 10.3017 10.5257 10.4825 10.6946Z" />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M13.9683 9.24363C13.9681 9.24476 13.9678 9.24589 13.9676 9.24701C13.9548 9.30874 13.941 9.37013 13.9263 9.43115C13.8392 9.79316 13.7198 10.1426 13.5713 10.4762C13.5665 10.487 13.5617 10.4977 13.5568 10.5084C13.4519 10.7406 13.3328 10.965 13.2007 11.1805C13.1462 11.2694 13.0896 11.3567 13.0307 11.4425C13.0248 11.4512 13.0188 11.4598 13.0129 11.4685C12.8486 11.7055 12.6679 11.9303 12.4724 12.1413C12.4037 12.2154 12.3332 12.2879 12.2609 12.3586C12.2599 12.3595 12.259 12.3605 12.258 12.3614C12.0451 12.5692 11.8171 12.7616 11.5758 12.9367C11.4884 13.0001 11.3993 13.0613 11.3085 13.12C11.3008 13.125 11.2931 13.13 11.2853 13.135C11.0803 13.2665 10.8668 13.386 10.646 13.4926C10.3589 13.6311 10.0594 13.7478 9.74949 13.8405C9.74659 13.8413 9.7437 13.8422 9.7408 13.8431C9.57194 13.8933 9.4 13.9364 9.22535 13.9721C9.08739 14.0002 8.94773 14.0237 8.80655 14.0424C8.79649 14.0437 8.78641 14.045 8.77633 14.0463C8.61254 14.0671 8.44671 14.0814 8.27913 14.089C8.18661 14.0932 8.09356 14.0953 8.00002 14.0953C7.44452 14.0953 6.90637 14.0209 6.39497 13.8817C6.37373 13.8759 6.35253 13.87 6.33138 13.864C6.06681 13.7889 5.80956 13.6963 5.56097 13.5877C3.40876 12.6469 1.90479 10.4991 1.90479 8.00002C1.90479 4.63372 4.63372 1.90479 8.00002 1.90479C10.4583 1.90479 12.5767 3.36009 13.5407 5.45615C13.5461 5.46795 13.5515 5.47977 13.5568 5.49161C13.6494 5.69632 13.731 5.90705 13.8008 6.12306C13.8384 6.23945 13.8727 6.35738 13.9034 6.47673C13.9523 6.66676 13.9922 6.86038 14.0228 7.05712C14.0705 7.36443 14.0953 7.67933 14.0953 8.00002C14.0953 8.00206 14.0953 8.0041 14.0953 8.00614C14.0952 8.03039 14.0951 8.05462 14.0948 8.0788C14.092 8.29577 14.0779 8.51001 14.0531 8.72095C14.0323 8.8976 14.0039 9.07194 13.9683 9.24363ZM7.38056 13.2978C7.55181 13.1246 7.83242 13.1185 8.01122 13.2855C8.02731 13.3005 8.04203 13.3164 8.05538 13.3331C8.41759 13.3294 8.77115 13.2896 9.11264 13.2171C9.2188 13.1303 9.35644 13.0988 9.48554 13.1237C9.62072 13.0846 9.75369 13.0403 9.88422 12.991C9.73278 12.8202 9.7348 12.5603 9.89556 12.3918C10.0662 12.2129 10.3512 12.2047 10.532 12.3736C10.6052 12.442 10.6501 12.5289 10.6663 12.6201C10.8669 12.5041 11.0592 12.3754 11.2421 12.2351C11.0789 12.3129 10.8773 12.288 10.7377 12.1576C10.5569 11.9888 10.5487 11.7069 10.7193 11.5279C10.89 11.349 11.1749 11.3409 11.3558 11.5097C11.5264 11.6691 11.5433 11.9291 11.4013 12.1082C11.9215 11.6771 12.3584 11.149 12.6844 10.5518C12.5767 10.5496 12.4694 10.5093 12.3849 10.4304C12.2041 10.2615 12.1958 9.97962 12.3665 9.8007C12.5372 9.62179 12.8221 9.61364 13.0029 9.7825C13.0094 9.78853 13.0156 9.7947 13.0216 9.80101C13.0609 9.69139 13.0968 9.58011 13.129 9.4673C13.031 9.30104 13.0504 9.08437 13.1907 8.9373C13.2126 8.91433 13.2364 8.89417 13.2616 8.87684C13.3088 8.59158 13.3334 8.29867 13.3334 8.00002C13.3334 7.99632 13.3334 7.99261 13.3333 7.9889C13.2705 7.96932 13.211 7.93573 13.1599 7.88798C12.9791 7.71912 12.9709 7.4372 13.1415 7.25828C13.1791 7.21894 13.2221 7.18785 13.2684 7.16509C13.1743 6.56694 12.9809 6.0018 12.7054 5.48711C12.5496 5.54486 12.3673 5.51456 12.2382 5.39394C12.0574 5.22508 12.0491 4.94316 12.2198 4.76425C12.2235 4.76033 12.2273 4.7565 12.2312 4.75275C12.1692 4.67217 12.105 4.5934 12.0387 4.51655C12.0274 4.53185 12.0151 4.54662 12.0016 4.56074C11.831 4.73965 11.546 4.7478 11.3652 4.57895C11.1844 4.41009 11.1762 4.12816 11.3468 3.94925C11.3656 3.9296 11.3857 3.91201 11.4069 3.89649C10.4829 3.12851 9.2954 2.66669 8.00002 2.66669C5.0545 2.66669 2.66669 5.0545 2.66669 8.00002C2.66669 9.39656 3.20345 10.6677 4.08192 11.6185C4.24183 11.5506 4.43414 11.5782 4.56875 11.7039C4.70268 11.8289 4.74192 12.016 4.68392 12.1774C4.78852 12.2605 4.89631 12.3398 5.00709 12.415C5.15522 12.3726 5.32155 12.4066 5.44182 12.5189C5.51107 12.5836 5.55501 12.6648 5.57327 12.7505C5.87702 12.906 6.19764 13.0332 6.53166 13.1287C6.52781 13.1253 6.52399 13.1219 6.52023 13.1184C6.33941 12.9495 6.33118 12.6676 6.50184 12.4887C6.67251 12.3097 6.95744 12.3016 7.13825 12.4704C7.31907 12.6393 7.3273 12.9212 7.15663 13.1001C7.09466 13.1651 7.01762 13.2076 6.93596 13.2272C7.08208 13.2568 7.23037 13.2804 7.38056 13.2978Z"
          />
        </svg>
      </Base>
    );
  }
}
