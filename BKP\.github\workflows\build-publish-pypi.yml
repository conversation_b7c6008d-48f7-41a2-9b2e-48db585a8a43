name: Build KeplerGL Python and NPM Packages

on: push

jobs:
  build_and_publish:
    runs-on: ubuntu-latest
    permissions:
      id-token: write

    steps:
      - uses: actions/checkout@v4

      # use Volta to manage yarn/node versions
      - uses: volta-cli/action@v4

      - name: Set up Python 3.9
        uses: actions/setup-python@v2
        with:
          python-version: 3.9

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install twine virtualenv

      - name: Build KeplerGL
        env:
          MapboxAccessTokenJupyter: ${{ secrets.mapbox_jupyter_token }}
          NODE_OPTIONS: --openssl-legacy-provider
        run: |
          python -m virtualenv venv
          source venv/bin/activate
          pip install jupyter jupyterlab jupyter-packaging
          cd bindings/kepler.gl-jupyter
          python setup.py sdist

      - name: Test KeplerGL
        run: |
          source venv/bin/activate
          pip install bindings/kepler.gl-jupyter/dist/*.tar.gz
          if [ ! -f "venv/share/jupyter/nbextensions/keplergl-jupyter/index.js" ]; then
            venv/bin/jupyter nbextension install --py --sys-prefix keplergl
            venv/bin/jupyter nbextension enable --py --sys-prefix keplergl
          fi
          venv/bin/jupyter nbconvert --execute bindings/kepler.gl-jupyter/notebooks/DataFrame.ipynb --to python
          python bindings/kepler.gl-jupyter/notebooks/DataFrame.py

      - name: Create artifact
        uses: actions/upload-artifact@v4
        with:
          name: keplergl-pypi
          path: bindings/kepler.gl-jupyter/dist/

      - name: Check Release Tag
        id: check-tag
        run: |
          if [[ ${{ github.event.ref }} =~ ^refs/tags/v[0-9]+\.[0-9]+\.[0-9]+([a|b][0-9])?-jupyter$ ]]; then
              echo ::set-output name=publish::true
          fi

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          registry-url: 'https://registry.npmjs.org'

      - name: Publish kepler-jupyter to NPM
        if: steps.check-tag.outputs.publish == 'true'
        env:
          NODE_AUTH_TOKEN: ${{secrets.npm_token}}
          MapboxAccessTokenJupyter: ${{ secrets.mapbox_jupyter_token }}
        run: |
          source venv/bin/activate
          cd bindings/kepler.gl-jupyter/js
          echo "//registry.npmjs.org/:_authToken=${{ secrets.npm_token }}" > .npmrc
          npm config set registry https://registry.npmjs.org/
          npm whoami
          npm config ls
          npm publish --verbose --access public

      - name: Publish KeplerGL to Pypi
        if: steps.check-tag.outputs.publish == 'true'
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          packages-dir: bindings/kepler.gl-jupyter/dist/
