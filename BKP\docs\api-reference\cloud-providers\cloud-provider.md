<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

### Table of Contents

-   [Provider][1]
    -   [downloadMap][4]
    -   [getAccessToken][7]
    -   [getMapUrl][8]
    -   [getShareUrl][10]
    -   [getUserName][12]
    -   [hasPrivateStorage][13]
    -   [hasSharingUrl][14]
    -   [listMaps][15]
    -   [login][17]
    -   [logout][19]
    -   [uploadMap][21]
-   [MapResponse][23]
-   [Viz][25]

## Provider

The default provider class

**Parameters**

-   `props` **[object][27]**
    -   `props.name` **[string][28]**
    -   `props.displayName` **[string][28]**
    -   `props.icon` **ReactElement** React element
    -   `props.thumbnail` **[object][27]** thumbnail size object
        -   `props.thumbnail.width` **[number][29]** thumbnail width in pixels
        -   `props.thumbnail.height` **[number][29]** thumbnail height in pixels

**Examples**

```javascript
const myProvider = new Provider({
 name: 'foo',
 displayName: 'Foo Storage'
 icon: Icon,
 thumbnail: {width: 300, height: 200}
})
```

### downloadMap

This method will be called when user select a map to load from the storage map viewer

**Parameters**

-   `loadParams` **any** the loadParams property of each visualization object

**Examples**

```javascript
async downloadMap(loadParams) {
 const mockResponse = {
   map: {
     datasets: [],
     config: {},
     info: {
       app: 'kepler.gl',
       created_at: ''
       title: 'test map',
       description: 'Hello this is my test dropbox map'
     }
   },
   // pass csv here if your provider currently only support save / load file as csv
   format: 'keplergl'
 };

 return downloadMap;
}
```

Returns **[MapResponse][30]** the map object containing dataset config info and format option

### getAccessToken

This method is called to determine whether user already logged in to this provider

Returns **[boolean][31]** true if a user already logged in

### getMapUrl

This method is called by kepler.gl demo app to pushes a new location to history, becoming the current location.

**Parameters**

-   `fullURL` **[boolean][31]** Whether to return the full url with domain, or just the location (optional, default `true`)

Returns **[string][28]** mapUrl

### getShareUrl

This method is called after user share a map, to display the share url.

**Parameters**

-   `fullUrl` **[boolean][31]** Whether to return the full url with domain, or just the location (optional, default `false`)

Returns **[string][28]** shareUrl

### getUserName

This method is called to get the user name of the current user. It will be displayed in the cloud provider tile.

Returns **[string][28]** true if a user already logged in

### hasPrivateStorage

Whether this provider support upload map to a private storage. If truthy, user will be displayed with the storage save icon on the top right of the side bar.

Returns **[boolean][31]**

### hasSharingUrl

Whether this provider support share map via a public url, if truthy, user will be displayed with a share map via url under the export map option on the top right of the side bar

Returns **[boolean][31]**

### listMaps

This method is called to get a list of maps saved by the current logged in user.

**Examples**

```javascript
async listMaps() {
   return [
     {
       id: 'a',
       title: 'My map',
       description: 'My first kepler map',
       imageUrl: 'http://',
       udpatedAt: 1582677787000,
       privateMap: false,
       loadParams: {}
     }
   ];
 }
```

Returns **[Array][32]&lt;[Viz][33]>** an array of Viz objects

### login

This method will be called when user click the login button in the cloud provider tile.
Upon login success, `onCloudLoginSuccess` has to be called to notify kepler.gl UI

**Parameters**

-   `onCloudLoginSuccess` **[function][34]** callbacks to be called after login success

### logout

This method will be called when user click the logout button under the cloud provider tile.
Upon login success, `onCloudLoginSuccess` has to be called to notify kepler.gl UI

**Parameters**

-   `onCloudLogoutSuccess` **[function][34]** callbacks to be called after logout success

### uploadMap

This method will be called to upload map for saving and sharing. Kepler.gl will package map data, config, title, description and thumbnail for upload to storage.
With the option to overwrite already saved map, and upload as private or public map.

**Parameters**

-   `param` **[Object][27]**
    -   `param.mapData` **[Object][27]** the map object
        -   `param.mapData.map` **[Object][27]** {datasets. config, info: {title, description}}
        -   `param.mapData.thumbnail` **[Blob][35]** A thumbnail of current map. thumbnail size can be defined by provider by this.thumbnail
    -   `param.options` **[Object][27]**  (optional, default `{}`)
        -   `param.options.overwrite` **[boolean][31]** whether user choose to overwrite already saved map under the same name
        -   `param.options.isPublic` **[boolean][31]** whether user wish to share the map with others. if isPublic is truthy, kepler will call this.getShareUrl() to display an URL they can share with others

## MapResponse

The returned object of `downloadMap`. The response object should contain: datasets: \[], config: {}, and info: {}
each dataset object should be {info: {id, label}, data: {...}}
to inform how kepler should process your data object, pass in `format`

Type: [Object][27]

### Properties

-   `map` **[Object][27]**
    -   `map.datasets` **[Array][32]&lt;[Object][27]>**
    -   `map.config` **[Object][27]**
    -   `map.info` **[Object][27]**
-   `format` **[string][28]** one of 'csv': csv file string, 'geojson': geojson object, 'row': row object, 'keplergl': datasets array saved using KeplerGlSchema.save

## Viz

Type: [Object][27]

### Properties

-   `id` **[string][28]** An unique id
-   `title` **[string][28]** The title of the map
-   `description` **[string][28]** The description of the map
-   `imageUrl` **[string][28]** The imageUrl of the map
-   `lastModification` **[number][29]** An epoch timestamp in milliseconds
-   `privateMap` **[boolean][31]** Optional, whether if this map is private to the user, or can be accessed by others via URL
-   `loadParams` **any** A property to be passed to `downloadMap`

[1]: #provider

[2]: #parameters

[3]: #examples

[4]: #downloadmap

[5]: #parameters-1

[6]: #examples-1

[7]: #getaccesstoken

[8]: #getmapurl

[9]: #parameters-2

[10]: #getshareurl

[11]: #parameters-3

[12]: #getusername

[13]: #hasprivatestorage

[14]: #hassharingurl

[15]: #listmaps

[16]: #examples-2

[17]: #login

[18]: #parameters-4

[19]: #logout

[20]: #parameters-5

[21]: #uploadmap

[22]: #parameters-6

[23]: #mapresponse

[24]: #properties

[25]: #viz

[26]: #properties-1

[27]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[28]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[29]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[30]: #mapresponse

[31]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

[32]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array

[33]: #viz

[34]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Statements/function

[35]: https://developer.mozilla.org/docs/Web/API/Blob
