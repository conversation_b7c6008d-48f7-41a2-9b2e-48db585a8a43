name: Node.js Package

on:
  release:
    types: [created]

jobs:
  publish-npm:
    runs-on: ubuntu-latest

    env:
      NPM_AUTH_TOKEN: ${{secrets.npm_token}}

    steps:
      - uses: actions/checkout@v4

      # use <PERSON><PERSON> to manage yarn/node versions
      - uses: volta-cli/action@v4

      - run: yarn install
      - run: yarn bootstrap
      - run: npm i -g npm@8.19.2

      - name: Login to NPM
        run: npm config set "//registry.npmjs.org/:_authToken=${NPM_AUTH_TOKEN}"

      - name: Publish
        run: npm publish --workspaces --access public
