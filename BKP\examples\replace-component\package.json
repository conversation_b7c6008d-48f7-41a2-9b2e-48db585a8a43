{"scripts": {"start": "node esbuild.config.mjs --start", "start:local": "NODE_ENV=local node esbuild.config.mjs --start"}, "dependencies": {"@kepler.gl/actions": "^3.0.0-alpha.1", "@kepler.gl/components": "^3.0.0-alpha.1", "@kepler.gl/reducers": "^3.0.0-alpha.1", "@kepler.gl/schemas": "^3.0.0-alpha.1", "@kepler.gl/styles": "^3.0.0-alpha.1", "esbuild": "^0.23.1", "global": "^4.3.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-palm": "^3.3.6", "react-redux": "^8.0.5", "react-virtualized": "^9.21.0", "redux-actions": "^2.2.1", "styled-components": "6.1.8"}, "devDependencies": {"esbuild-plugin-replace": "^1.4.0"}, "engines": {"node": ">=18"}, "volta": {"node": "18.18.2", "yarn": "4.4.0"}, "packageManager": "yarn@4.4.0"}