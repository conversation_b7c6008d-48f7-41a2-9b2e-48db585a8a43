// SPDX-License-Identifier: MIT
// Copyright contributors to the kepler.gl project

import React from 'react';
import {SidePanelItem, SidePanelProps} from '../types';
import {RGBColor} from '@kepler.gl/types';
import {Icons} from '@kepler.gl/components';
import TurfeirasPanel from '../../../turfeiras/TurfeirasPanel';

export type CustomPanelsStaticProps<P> = {
  panels: SidePanelItem[];
  getProps?: (props: SidePanelProps) => P;
};
export type CustomPanelsProps = {
  activeSidePanel: string | null;
  updateTableColor: (dataId: string, newColor: RGBColor) => void;
};

// Ícone personalizado para o painel de Turfeiras
const PeatlandIcon = (props: any) => (
  <svg
    viewBox="0 0 24 24"
    width={props.height || '16px'}
    height={props.height || '16px'}
    fill={props.fill || 'currentColor'}
  >
    <path d="M19,16.8c0.2-0.5,0.2-1.1,0-1.6l-1.4-3.2C17.1,10.9,16.1,9,14.2,9h-1.8h-1.8c-0.9,0-1.7,0.5-2.1,1.4L8,11.8 c-0.4,0.8-1.2,1.3-2.1,1.3H4v5.1h2.4c0.4,0,0.7,0.1,1.1,0.2C8.3,18.8,9.3,19,10.3,19h3.7c1.3,0,2.4-0.7,3-1.8L19,16.8z" />
    <path d="M3,10h3c0.5,0,1,0.2,1.3,0.5C7.6,10.7,8,11.1,8,11.6c0,0.5-0.2,1-0.5,1.3C7.2,13.2,6.8,13.4,6.3,13.4H3V10z" />
    <path d="M3,5h4c0.5,0,1,0.2,1.3,0.5C8.6,5.8,9,6.2,9,6.7c0,0.5-0.2,1-0.5,1.3C8.2,8.3,7.8,8.5,7.3,8.5H3V5z" />
    <path d="M11,5h4c0.5,0,1,0.2,1.3,0.5c0.3,0.3,0.7,0.7,0.7,1.2c0,0.5-0.2,1-0.5,1.3c-0.3,0.3-0.7,0.5-1.2,0.5h-4V5z" />
    <path d="M15,15h4c0.5,0,1,0.2,1.3,0.5c0.3,0.3,0.7,0.7,0.7,1.2c0,0.5-0.2,1-0.5,1.3c-0.3,0.3-0.7,0.5-1.2,0.5h-4V15z" />
    <path d="M15,10h4c0.5,0,1,0.2,1.3,0.5c0.3,0.3,0.7,0.7,0.7,1.2c0,0.5-0.2,1-0.5,1.3c-0.3,0.3-0.7,0.5-1.2,0.5h-4V10z" />
    <path d="M11,15h1.5c0.5,0,1,0.2,1.3,0.5c0.3,0.3,0.7,0.7,0.7,1.2c0,0.5-0.2,1-0.5,1.3c-0.3,0.3-0.7,0.5-1.2,0.5H11V15z" />
  </svg>
);

// Componente customizado para injetar o painel de Turfeiras na barra lateral
function CustomPanelsFactory<P>() {
  const CustomPanels: React.FC<CustomPanelsProps> & CustomPanelsStaticProps<P> = (props) => {
    // Renderizar o painel de Turfeiras quando estiver ativo
    if (props.activeSidePanel === 'turfeiras') {
      return <TurfeirasPanel {...props} />;
    }
    return <div />;
  };
  
  // Lista de painéis adicionais a serem exibidos na barra lateral
  CustomPanels.panels = [
    {
      id: 'turfeiras',
      label: 'TurfeirasBrasil',
      iconComponent: PeatlandIcon
    }
  ];

  // Selector de props para o painel de Turfeiras
  CustomPanels.getProps = (sidePanelProps: SidePanelProps) => ({
    // Props do Kepler.gl que queremos acessar no painel de Turfeiras
    datasets: sidePanelProps.datasets,
    layers: sidePanelProps.layers,
    mapState: sidePanelProps.mapState,
    filters: sidePanelProps.filters,
    visState: sidePanelProps.visState,
    uiState: sidePanelProps.uiState,
    mapStyle: sidePanelProps.mapStyle,
    dispatch: sidePanelProps.dispatch,
    // Adicionamos outras props que podem ser necessárias para o TurfeirasPanel
    layerBlending: sidePanelProps.layerBlending,
    layerClasses: sidePanelProps.layerClasses,
    layerOrder: sidePanelProps.layerOrder,
    interactionConfig: sidePanelProps.interactionConfig
  } as P);

  return CustomPanels;
}

export default CustomPanelsFactory;
