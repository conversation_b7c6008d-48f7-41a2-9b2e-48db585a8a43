{"name": "@kepler.gl/deckgl-layers", "author": "<PERSON> <<EMAIL>>", "version": "3.1.8", "description": "kepler.gl constants used by kepler.gl components, actions and reducers", "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "keywords": ["babel", "es6", "react", "webgl", "visualization", "deck.gl"], "repository": {"type": "git", "url": "https://github.com/keplergl/kepler.gl.git"}, "scripts": {"build": "rm -fr dist && babel src --out-dir dist --source-maps inline --extensions '.ts,.tsx,.js,.jsx' --ignore '**/*.d.ts'", "build:umd": "NODE_OPTIONS=--openssl-legacy-provider webpack --config ./webpack/umd.js --progress --env.prod", "build:types": "tsc --project ./tsconfig.production.json", "prepublishOnly": "babel-node ../../scripts/license-header/bin --license ../../FILE-HEADER && yarn build && yarn build:types", "stab": "mkdir -p dist && touch dist/index.js"}, "files": ["dist", "umd"], "dependencies": {"@danmarshall/deckgl-typings": "4.9.22", "@deck.gl/aggregation-layers": "^8.9.27", "@deck.gl/core": "^8.9.27", "@deck.gl/geo-layers": "^8.9.27", "@deck.gl/layers": "^8.9.27", "@kepler.gl/constants": "3.1.8", "@kepler.gl/types": "3.1.8", "@kepler.gl/utils": "3.1.8", "@loaders.gl/wms": "4.3.2", "@luma.gl/constants": "^8.5.20", "@luma.gl/core": "^8.5.20", "@mapbox/geo-viewport": "^0.4.1", "@mapbox/vector-tile": "^1.3.1", "@math.gl/web-mercator": "^3.6.2", "@types/d3-array": "^2.8.0", "@types/geojson": "^7946.0.8", "@types/lodash": "4.17.5", "@types/supercluster": "^7.1.0", "d3-array": "^2.8.0", "global": "^4.3.0", "lodash": "4.17.21", "pbf": "^3.1.0", "supercluster": "^7.1.0", "viewport-mercator-project": "^6.0.0"}, "nyc": {"sourceMap": false, "instrument": false}, "maintainers": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "engines": {"node": ">=18"}, "volta": {"node": "18.18.2", "yarn": "4.4.0"}, "packageManager": "yarn@4.4.0"}