// SPDX-License-Identifier: MIT
// Copyright contributors to the kepler.gl project

import React from 'react';

// Importar o componente TurfeirasPanel do diretório correto
let TurfeirasComponent;
try {
  // Tentar importar o componente real das turfeiras
  const TurfeirasModule = require('../../../../src/turfeiras/TurfeirasPanel.tsx');
  TurfeirasComponent = TurfeirasModule.default || TurfeirasModule.TurfeirasPanel;
} catch (error) {
  console.log('Fallback: Usando componente simples para turfeiras');
  // Fallback: Componente simples caso não encontre o arquivo
  TurfeirasComponent = () => {
    return (
      <div style={{ padding: '20px', color: '#333' }}>
        <h2 style={{ color: '#2d5a3d', marginBottom: '16px' }}>🌿 TurfeirasBrasil</h2>
        <p>Módulo de turfeiras carregado com sucesso!</p>
        <div style={{ background: '#f8f9fa', padding: '16px', borderRadius: '8px', marginTop: '16px' }}>
          <h3>📊 Dados de Turfeiras</h3>
          <ul>
            <li>5 turfeiras mapeadas</li>
            <li>Pantanal Norte (MT)</li>
            <li>Serra do Cipó (MG)</li>
            <li>Chapada dos Veadeiros (GO)</li>
            <li>Amazônia Central (AM)</li>
            <li>Mata Atlântica (SP)</li>
          </ul>
        </div>
        <div style={{ background: '#e8f5e8', padding: '16px', borderRadius: '8px', marginTop: '16px' }}>
          <h3>🔬 Análises Disponíveis</h3>
          <ul>
            <li>Carbono armazenado</li>
            <li>pH e nutrientes</li>
            <li>Biodiversidade</li>
            <li>Estado de conservação</li>
          </ul>
        </div>
        <div style={{ background: '#fff3cd', padding: '16px', borderRadius: '8px', marginTop: '16px' }}>
          <h3>⚠️ Alertas</h3>
          <p>2 turfeiras precisam de atenção:</p>
          <ul>
            <li>Pantanal Norte: Risco de drenagem</li>
            <li>Mata Atlântica: Pressão urbana</li>
          </ul>
        </div>
        <div style={{ background: '#d1ecf1', padding: '16px', borderRadius: '8px', marginTop: '16px' }}>
          <h3>📈 Estatísticas</h3>
          <p><strong>Área total monitorada:</strong> 2.847 hectares</p>
          <p><strong>Carbono estimado:</strong> 125.6 toneladas/hectare</p>
          <p><strong>Última atualização:</strong> {new Date().toLocaleDateString('pt-BR')}</p>
        </div>
      </div>
    );
  };
}
        </ul>
      </div>
      <div style={{ background: '#e8f5e8', padding: '16px', borderRadius: '8px', marginTop: '16px' }}>
        <h3>🤖 IA Integrada</h3>
        <p>Assistente especializado em análise de turfeiras tropicais.</p>
      </div>
    </div>
  );
};

// Ícone personalizado para o painel de Turfeiras
const PeatlandIcon = (props) => (
  <svg
    viewBox="0 0 24 24"
    width={props.height || '16px'}
    height={props.height || '16px'}
    fill={props.fill || 'currentColor'}
  >
    <path d="M8 2C6.5 2 5.5 3 5.5 4.5C5.5 6 6.5 7 8 7C9.5 7 10.5 6 10.5 4.5C10.5 3 9.5 2 8 2Z"/>
    <path d="M3 8C3 6.5 4 5.5 5.5 5.5C7 5.5 8 6.5 8 8C8 9.5 7 10.5 5.5 10.5C4 10.5 3 9.5 3 8Z"/>
    <path d="M8 8C8 6.5 9 5.5 10.5 5.5C12 5.5 13 6.5 13 8C13 9.5 12 10.5 10.5 10.5C9 10.5 8 9.5 8 8Z"/>
    <path d="M5.5 10.5C4 10.5 3 11.5 3 13C3 14.5 4 15.5 5.5 15.5C7 15.5 8 14.5 8 13C8 11.5 7 10.5 5.5 10.5Z"/>
    <path d="M10.5 10.5C9 10.5 8 11.5 8 13C8 14.5 9 15.5 10.5 15.5C12 15.5 13 14.5 13 13C13 11.5 12 10.5 10.5 10.5Z"/>
  </svg>
);

function CustomPanelsFactory() {
  const CustomPanels = (props) => {
    // Renderizar o painel de Turfeiras quando estiver ativo
    if (props.activeSidePanel === 'turfeiras') {
      return <TurfeirasComponent {...props} />;
    }
    return null;
  };

  // Lista de painéis customizados
  CustomPanels.panels = [
    {
      id: 'turfeiras',
      label: 'TurfeirasBrasil',
      iconComponent: PeatlandIcon
    }
  ];

  // Função para extrair props do estado do Kepler.gl
  CustomPanels.getProps = (props) => ({
    datasets: props.datasets,
    layers: props.layers,
    mapState: props.mapState,
    filters: props.filters,
    visState: props.visState,
    uiState: props.uiState,
    mapStyle: props.mapStyle,
    dispatch: props.dispatch
  });

  return CustomPanels;
}

export default CustomPanelsFactory;
