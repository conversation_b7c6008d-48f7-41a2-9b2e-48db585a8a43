# Adding Data Layers

The term "Layer" refers to a layer of data visualization. For example, you might add a point layer to visualize all the instances where taxi trips began, as in the map of New York City below. 

![Sample NYC Map](https://d1a3f4spazzrp4.cloudfront.net/kepler.gl/documentation/image43.png "Sample NYC Map")

Each blue dot represents the point (latitude and longitude). Layers work like paint—you can build up multiple layers to change the appearance of the canvas. You might create a second point layer to show trip drop-off locations. The map then looks like this:

![Sample NYC Map with colors](https://d1a3f4spazzrp4.cloudfront.net/kepler.gl/documentation/image6.png "Sample NYC Map with colors")

Learn more about the [types of layers](../../c-types-of-layers/README.md) available in Kepler.gl.

[Back to table of contents](../../README.md)
