{"scripts": {"start": "webpack-dev-server --progress --hot --open", "start-local": "webpack-dev-server --env.es6 --progress --hot --open"}, "dependencies": {"@kepler.gl/actions": "^3.0.0-alpha.1", "@kepler.gl/components": "^3.0.0-alpha.1", "@kepler.gl/reducers": "^3.0.0-alpha.1", "global": "^4.3.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-modal": "^3.1.10", "react-palm": "^3.3.6", "react-redux": "^8.0.5", "react-virtualized": "^9.21.0", "redux-actions": "^2.2.1", "styled-components": "6.1.8"}, "devDependencies": {"@babel/core": "^7.12.1", "@babel/plugin-transform-class-properties": "^7.12.1", "@babel/plugin-transform-export-namespace-from": "^7.12.1", "@babel/plugin-transform-modules-commonjs": "^7.12.1", "@babel/plugin-transform-optional-chaining": "^7.12.1", "@babel/plugin-transform-runtime": "^7.12.1", "@babel/plugin-transform-typescript": "^7.16.8", "@babel/preset-env": "^7.12.1", "@babel/preset-react": "^7.12.1", "@babel/preset-typescript": "^7.16.7", "babel-loader": "^8.0.0", "webpack": "^4.29.0", "webpack-cli": "^3.2.1", "webpack-dev-middleware": "^3.5.1", "webpack-dev-server": "^3.1.14", "webpack-hot-middleware": "^2.24.3"}}