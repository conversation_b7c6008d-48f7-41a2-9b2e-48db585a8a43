{"cells": [{"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame(\n", "    {'City': ['Buenos Aires', 'Brasilia', 'Santiago', 'Bogota', 'Caracas'],\n", "     'Country': ['Argentina', 'Brazil', 'Chile', 'Colombia', 'Venezuela'],\n", "     'Latitude': [-34.58, -15.78, -33.45, 4.60, 10.48],\n", "     'Longitude': [-58.66, -47.91, -70.66, -74.08, -66.86],\n", "     'Time': ['2019-09-01 08:00','2019-09-01 09:00','2019-09-01 10:00','2019-09-01 11:00', '2019-09-01 12:00']\n", "    })"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["User Guide: https://docs.kepler.gl/docs/keplergl-jupyter\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b85241da03b2474bb2c5cb6cef155a80", "version_major": 2, "version_minor": 0}, "text/plain": ["KeplerGl(data={'data_1':            City    Country  Latitude  Longitude              Time\n", "0  Buenos Aires  Ar…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import keplergl\n", "w1 = keplergl.KeplerGl(height=600, data={'data_1': df})\n", "w1"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Map saved to keplergl_map.html!\n"]}], "source": ["w1.save_to_html(file_name='keplergl_map.html')"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 4}