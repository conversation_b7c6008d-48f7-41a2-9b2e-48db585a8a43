#!/bin/bash

# Script de inicialização para o servidor de IA - Turfeiras V3
# Arquivo: start.sh

set -e

echo "=== Iniciando Servidor de IA Turfeiras V3 ==="

# Verificar se o Python está instalado
if ! command -v python3 &> /dev/null; then
    echo "Erro: Python 3 não está instalado"
    exit 1
fi

# Verificar se o pip está instalado
if ! command -v pip3 &> /dev/null; then
    echo "Erro: pip3 não está instalado"
    exit 1
fi

# Definir diretório de trabalho
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "Diretório de trabalho: $SCRIPT_DIR"

# Criar ambiente virtual se não existir
if [ ! -d "venv" ]; then
    echo "Criando ambiente virtual..."
    python3 -m venv venv
fi

# Ativar ambiente virtual
echo "Ativando ambiente virtual..."
source venv/bin/activate

# Atualizar pip
echo "Atualizando pip..."
pip install --upgrade pip

# Instalar dependências
echo "Instalando dependências..."
if [ -f "requirements-ml.txt" ]; then
    pip install -r requirements-ml.txt
else
    echo "Aviso: requirements-ml.txt não encontrado"
fi

# Verificar se o gunicorn está instalado
if ! command -v gunicorn &> /dev/null; then
    echo "Instalando gunicorn..."
    pip install gunicorn
fi

# Criar diretório para modelos salvos
mkdir -p models/saved

# Configurar variáveis de ambiente
export PYTHONPATH="$SCRIPT_DIR:$PYTHONPATH"
export FLASK_APP="server.py"
export FLASK_ENV="${FLASK_ENV:-production}"

# Definir configurações padrão
export PORT="${PORT:-5000}"
export WORKERS="${WORKERS:-4}"
export TIMEOUT="${TIMEOUT:-300}"
export DEBUG="${DEBUG:-false}"

echo "Configurações:"
echo "  - Porta: $PORT"
echo "  - Workers: $WORKERS"
echo "  - Timeout: $TIMEOUT"
echo "  - Debug: $DEBUG"
echo "  - Ambiente: $FLASK_ENV"

# Verificar se o servidor está funcionando
echo "Verificando dependências..."
python3 -c "
try:
    import numpy, pandas, sklearn, rasterio, flask
    print('✓ Dependências básicas OK')
except ImportError as e:
    print(f'✗ Erro nas dependências: {e}')
    exit(1)
"

# Função para inicializar em modo desenvolvimento
start_dev() {
    echo "Iniciando em modo desenvolvimento..."
    export DEBUG="true"
    export FLASK_ENV="development"
    python3 server.py
}

# Função para inicializar em modo produção
start_prod() {
    echo "Iniciando em modo produção com gunicorn..."
    gunicorn --config gunicorn_config.py server:app
}

# Função para executar testes
run_tests() {
    echo "Executando testes..."
    python3 -c "
from models.random_forest.geomorph_classifier import GeomorphClassifier
import json

# Teste básico
print('Testando classificador...')
classifier = GeomorphClassifier()

# Geometria de teste
test_geometry = {
    'type': 'Polygon',
    'coordinates': [[
        [-45.0, -22.0],
        [-45.0, -22.1],
        [-45.1, -22.1],
        [-45.1, -22.0],
        [-45.0, -22.0]
    ]]
}

result = classifier.classify_geomorphology(test_geometry)
print('✓ Classificação funcionando:')
print(f'  Classe: {result[\"class\"]}')
print(f'  Confiança: {result[\"confidence\"]:.2f}')
print('✓ Testes concluídos com sucesso')
"
}

# Função para mostrar status
show_status() {
    echo "=== Status do Servidor ==="
    if pgrep -f "server.py\|gunicorn" > /dev/null; then
        echo "✓ Servidor está rodando"
        echo "PIDs:"
        pgrep -f "server.py\|gunicorn"
    else
        echo "✗ Servidor não está rodando"
    fi
}

# Função para parar o servidor
stop_server() {
    echo "Parando servidor..."
    pkill -f "server.py\|gunicorn" || true
    echo "✓ Servidor parado"
}

# Função para mostrar ajuda
show_help() {
    echo "Uso: $0 [comando]"
    echo ""
    echo "Comandos:"
    echo "  start       - Iniciar em modo produção (padrão)"
    echo "  dev         - Iniciar em modo desenvolvimento"
    echo "  test        - Executar testes"
    echo "  status      - Mostrar status do servidor"
    echo "  stop        - Parar o servidor"
    echo "  restart     - Reiniciar o servidor"
    echo "  help        - Mostrar esta ajuda"
    echo ""
    echo "Variáveis de ambiente:"
    echo "  PORT        - Porta do servidor (padrão: 5000)"
    echo "  WORKERS     - Número de workers (padrão: 4)"
    echo "  TIMEOUT     - Timeout em segundos (padrão: 300)"
    echo "  DEBUG       - Modo debug (padrão: false)"
    echo "  FLASK_ENV   - Ambiente Flask (padrão: production)"
}

# Processar argumentos
case "${1:-start}" in
    "start")
        start_prod
        ;;
    "dev")
        start_dev
        ;;
    "test")
        run_tests
        ;;
    "status")
        show_status
        ;;
    "stop")
        stop_server
        ;;
    "restart")
        stop_server
        sleep 2
        start_prod
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        echo "Comando inválido: $1"
        show_help
        exit 1
        ;;
esac
